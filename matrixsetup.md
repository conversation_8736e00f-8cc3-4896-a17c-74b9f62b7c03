# Matrix Server Setup Guide for NxtAcre Farm Management Platform

This guide provides instructions for setting up and configuring an external Matrix server for use with the NxtAcre Farm Management Platform. The platform uses Matrix for real-time chat and communication features.

## Overview

The NxtAcre platform integrates with a Matrix server for chat functionality. While the project includes a Docker-based Matrix server setup, this guide focuses on configuring an external Matrix server for production use.

## Prerequisites

- A server or VPS with at least 2GB RAM and 1 CPU core
- Domain name with DNS configured (e.g., `chat.nxtacre.com`)
- SSL certificate for your domain
- PostgreSQL database server (can be shared with the main application)
- Basic knowledge of Linux server administration

## Installation Steps

### 1. Set Up a Server

1. Provision a server with Ubuntu 20.04 or later
2. Update the system:
   ```bash
   sudo apt update && sudo apt upgrade -y
   ```
3. Install required dependencies:
   ```bash
   sudo apt install -y build-essential python3-dev python3-pip python3-setuptools python3-venv libffi-dev libssl-dev libjpeg-dev libxslt1-dev postgresql-client
   ```

### 2. Set Up Matrix Synapse

1. Create a Python virtual environment:
   ```bash
   mkdir -p ~/synapse
   cd ~/synapse
   python3 -m venv env
   source env/bin/activate
   ```

2. Install Matrix Synapse:
   ```bash
   pip install --upgrade pip
   pip install matrix-synapse
   ```

3. Generate an initial configuration:
   ```bash
   python -m synapse.app.homeserver \
     --server-name chat.nxtacre.com \
     --config-path homeserver.yaml \
     --generate-config \
     --report-stats=no
   ```

### 3. Configure PostgreSQL Database

1. Create a PostgreSQL user and database for Matrix:
   ```bash
   sudo -u postgres psql
   ```

2. In the PostgreSQL prompt:
   ```sql
   CREATE USER matrix WITH PASSWORD 'your_secure_password';
   CREATE DATABASE matrix OWNER matrix;
   \q
   ```

3. Create the Matrix schema in your existing NxtAcre database (if you want to use the same database):
   ```bash
   sudo -u postgres psql -d farmbooks
   ```

4. In the PostgreSQL prompt:
   ```sql
   CREATE SCHEMA matrix;
   GRANT ALL ON SCHEMA matrix TO postgres;
   \q
   ```

### 4. Configure Matrix Synapse

Edit the `homeserver.yaml` file to match the following configuration:

```yaml
# Configuration file for Synapse.
#
# This is a YAML file: see [1] for a quick introduction. Note in particular
# that *indentation is important*: all the elements of a list or dictionary
# should have the same indentation.
#
# [1] https://docs.ansible.com/ansible/latest/reference_appendices/YAMLSyntax.html
#
# For more information on how to configure Synapse, including a complete accounting of
# each option, go to docs/usage/configuration/config_documentation.md or
# https://element-hq.github.io/synapse/latest/usage/configuration/config_documentation.html
server_name: "chat.nxtacre.com"
pid_file: "/var/run/matrix-synapse.pid"

# Database configuration
database:
   name: psycopg2
   args:
      user: "doadmin"
      password: "AVNS_1A15p7ZlMpxQDk6GpEb"
      database: "nxtacre"
      #    host: "private-db-new-do-user-********-0.i.db.ondigitalocean.com"
      host: "db-new-do-user-********-0.i.db.ondigitalocean.com"
      port: 25060
      cp_min: 5
      cp_max: 10
      options: "-c search_path=matrix"
      sslmode: require

# Registration
enable_registration: false
registration_shared_secret: "1b34db2b74dd9d0199448d9fee6de8f6a7f12eaa62265af0"

# Media storage
media_store_path: "/etc/matrix-synapse/media_store"

# Listeners
listeners:
   - port: 8008
     tls: false
     type: http
     x_forwarded: true
     bind_addresses: ['::1', '127.0.0.1']
     resources:
        - names: [client, federation]
          compress: true

# Federation
federation_domain_whitelist:
   - "chat.nxtacre.com"

# Rate limiting
rc_messages_per_second: 0.2
rc_message_burst_count: 10.0

# Retention policy
retention:
   enabled: true
   default_policy:
      min_lifetime: 1d
      max_lifetime: 365d

# URL previews
url_preview_enabled: true
url_preview_ip_range_blacklist:
   - '*********/8'
   - '10.0.0.0/8'
   - '**********/12'
   - '***********/16'
   - '**********/10'
   - '***********/16'
   - '::1/128'
   - 'fe80::/64'
   - 'fc00::/7'

# Encryption
encryption_enabled_by_default_for_room_type: "all"

# Presence
presence:
   enabled: true

# Push notifications
push:
   include_content: true

# TURN server (for voice/video calls)
turn_uris: []
turn_shared_secret: "t<.HClD,l_jORIXf|y).fq-J#I|5+=e=Tkc)`CIdBX}Z4f]PT.QP7;/"
turn_user_lifetime: 86400000
turn_allow_guests: true

# Experimental features
experimental_features:
   spaces_enabled: true

log_config: "/etc/matrix-synapse/chat.nxtacre.com.log.config"
report_stats: false
macaroon_secret_key: "6AKHpl^hoY&AqT^myGR9f8YH*J7sZVHyzI^S6;NLp-uf@=FgNE"
form_secret: "4o36EmvlM9cL7NszDp5C2zG:2M9Z5yI_7s4j;ly=.,.w06wQxo"
signing_key_path: "/etc/matrix-synapse/chat.nxtacre.com.signing.key"
trusted_key_servers:
   - server_name: "matrix.org"
suppress_key_server_warning: true
```

Replace the following values with your actual configuration:
- `chat.nxtacre.com` with your Matrix server domain
- `/home/<USER>/synapse/` with your actual installation path
- `your_database_password` with your PostgreSQL password
- `your_database_host` with your PostgreSQL host
- `your_registration_shared_secret` with a secure random string
- `your_jwt_secret` with the same JWT secret used by your NxtAcre application
- `your_turn_shared_secret` with a secure random string

### 5. Set Up Nginx as a Reverse Proxy

1. Install Nginx:
   ```bash
   sudo apt install -y nginx certbot python3-certbot-nginx
   ```

2. Create an Nginx configuration file:
   ```bash
   sudo nano /etc/nginx/sites-available/matrix
   ```

3. Add the following configuration:
   ```nginx
   server {
       listen 80;
       server_name chat.nxtacre.com;
       
       location / {
           return 301 https://$host$request_uri;
       }
   }

   server {
       listen 443 ssl;
       server_name chat.nxtacre.com;

       ssl_certificate /etc/letsencrypt/live/chat.nxtacre.com/fullchain.pem;
       ssl_certificate_key /etc/letsencrypt/live/chat.nxtacre.com/privkey.pem;
       
       location / {
           proxy_pass http://localhost:8008;
           proxy_set_header X-Forwarded-For $remote_addr;
           proxy_set_header X-Forwarded-Proto $scheme;
           proxy_set_header Host $host;
           
           # WebSocket support
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection "upgrade";
       }
   }
   ```

4. Enable the site and get SSL certificate:
   ```bash
   sudo ln -s /etc/nginx/sites-available/matrix /etc/nginx/sites-enabled/
   sudo certbot --nginx -d chat.nxtacre.com
   sudo systemctl restart nginx
   ```

### 6. Create a Systemd Service

1. Create a systemd service file:
   ```bash
   sudo nano /etc/systemd/system/matrix-synapse.service
   ```

2. Add the following content:
   ```ini
   [Unit]
   Description=Matrix Synapse
   After=network.target

   [Service]
   Type=simple
   User=ubuntu
   WorkingDirectory=/home/<USER>/synapse
   ExecStart=/home/<USER>/synapse/env/bin/python -m synapse.app.homeserver --config-path=/home/<USER>/synapse/homeserver.yaml
   Restart=always
   RestartSec=3

   [Install]
   WantedBy=multi-user.target
   ```

3. Enable and start the service:
   ```bash
   sudo systemctl daemon-reload
   sudo systemctl enable matrix-synapse
   sudo systemctl start matrix-synapse
   ```

### 7. Create an Admin User

1. Create an admin user:
   ```bash
   cd ~/synapse
   source env/bin/activate
   register_new_matrix_user -c homeserver.yaml http://localhost:8008
   ```

2. Follow the prompts to create an admin user.

## NxtAcre Platform Configuration

After setting up the external Matrix server, you need to update the NxtAcre platform configuration to use it:

1. Update the following environment variables in your NxtAcre application's `.env` file:

   ```
   MATRIX_SERVER_URL=https://chat.nxtacre.com
   MATRIX_DOMAIN=chat.nxtacre.com
   MATRIX_ADMIN_USERNAME=your_admin_username
   MATRIX_ADMIN_PASSWORD=your_admin_password
   REGISTRATION_SHARED_SECRET=your_registration_shared_secret
   VITE_MATRIX_SERVER_URL=https://chat.nxtacre.com
   VITE_MATRIX_DOMAIN=chat.nxtacre.com
   ```

2. Restart the NxtAcre application to apply the changes.

## Verification

To verify that the Matrix server is properly configured and working with the NxtAcre platform:

1. Log in to the NxtAcre platform
2. Navigate to the chat feature
3. Create a new chat room
4. Send a test message
5. Verify that the message appears in the chat

## Troubleshooting

### Matrix Server Logs

Check the Matrix server logs for errors:
```bash
sudo journalctl -u matrix-synapse -f
```

### Connection Issues

If the NxtAcre platform cannot connect to the Matrix server:

1. Verify that the Matrix server is running:
   ```bash
   sudo systemctl status matrix-synapse
   ```

2. Check that the Matrix server is accessible from the NxtAcre server:
   ```bash
   curl -I https://chat.nxtacre.com/_matrix/client/versions
   ```

3. Verify that the environment variables are correctly set in the NxtAcre application.

### Database Issues

If there are database connection issues:

1. Verify that the PostgreSQL server is running and accessible
2. Check that the database credentials in the Matrix configuration are correct
3. Ensure that the Matrix schema exists in the database

## Maintenance

### Backups

Set up regular backups of:
1. The Matrix database
2. The media store directory
3. The homeserver.yaml configuration file

### Updates

To update Matrix Synapse:

1. Stop the service:
   ```bash
   sudo systemctl stop matrix-synapse
   ```

2. Activate the virtual environment and update:
   ```bash
   cd ~/synapse
   source env/bin/activate
   pip install --upgrade matrix-synapse
   ```

3. Start the service:
   ```bash
   sudo systemctl start matrix-synapse
   ```

## Security Considerations

1. Keep your server and Matrix Synapse updated with security patches
2. Use strong passwords for all accounts
3. Regularly rotate the registration shared secret
4. Consider implementing additional security measures like fail2ban
5. Monitor server logs for suspicious activity

## Conclusion

You have successfully set up an external Matrix server for use with the NxtAcre Farm Management Platform. This configuration provides a production-ready chat solution that integrates seamlessly with the platform.


************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
-----BEGIN CERTIFICATE-----
MIIEmDCCA4CgAwIBAgIUBy4s82eEkqigw9wtRqs73RrLRLwwDQYJKoZIhvcNAQEL
BQAwgYsxCzAJBgNVBAYTAlVTMRkwFwYDVQQKExBDbG91ZEZsYXJlLCBJbmMuMTQw
MgYDVQQLEytDbG91ZEZsYXJlIE9yaWdpbiBTU0wgQ2VydGlmaWNhdGUgQXV0aG9y
aXR5MRYwFAYDVQQHEw1TYW4gRnJhbmNpc2NvMRMwEQYDVQQIEwpDYWxpZm9ybmlh
MB4XDTI1MDYxNzE2NDIwMFoXDTQwMDYxMzE2NDIwMFowYjEZMBcGA1UEChMQQ2xv
dWRGbGFyZSwgSW5jLjEdMBsGA1UECxMUQ2xvdWRGbGFyZSBPcmlnaW4gQ0ExJjAk
BgNVBAMTHUNsb3VkRmxhcmUgT3JpZ2luIENlcnRpZmljYXRlMIIBIjANBgkqhkiG
9w0BAQEFAAOCAQ8AMIIBCgKCAQEAynYPD6EwZAWT1mTzTCUXHzyeGBfFA3LTnchO
NzniS8HJWWNDCV2zHoZO6uOHLU2SFgkyGJczqDV48Lj22DpqEAsKhsnOpYFKdUud
fd+t1RcsSm95tzjEb6tCvBe10943KAdMwfHB/o06c99rsuxWkRmg05JHdADqsvA0
y0vHjEjflFbZ8pywB9fBlLlwdjzpRBoHXivyTuGxdL2guckpEWoVbTZFldUEs4CS
3mhKkJtUX4UsUUHZuK54q3vLQTP7mFHSwr3axPs2OAl1wffDr/GiisyBr/PXoMfe
dpetzGkWVseAyitauiHn59Oyb+9Ur/9R6KuKl9JxKbT6Nu34wwIDAQABo4IBGjCC
ARYwDgYDVR0PAQH/BAQDAgWgMB0GA1UdJQQWMBQGCCsGAQUFBwMCBggrBgEFBQcD
ATAMBgNVHRMBAf8EAjAAMB0GA1UdDgQWBBRD70SKT+y1tZ9CisFVknIzrsMA/zAf
BgNVHSMEGDAWgBQk6FNXXXw0QIep65TbuuEWePwppDBABggrBgEFBQcBAQQ0MDIw
MAYIKwYBBQUHMAGGJGh0dHA6Ly9vY3NwLmNsb3VkZmxhcmUuY29tL29yaWdpbl9j
YTAbBgNVHREEFDASghBjaGF0Lm54dGFjcmUuY29tMDgGA1UdHwQxMC8wLaAroCmG
J2h0dHA6Ly9jcmwuY2xvdWRmbGFyZS5jb20vb3JpZ2luX2NhLmNybDANBgkqhkiG
9w0BAQsFAAOCAQEAMsPOURaTLoLsvmmbWsIr9F6Y3epXaTwVjywLDOTKKuZ2qm6Y
OB6sDPe7Rcposas4sinqJL8d3e1aocjgkEG0APNbvagG+U4mGE2akls5zd2MFqVQ
uDw2lHLRUv8y+PHBcCV4rdc0xkcBV1hzwypsjgZbBk7NDyHusn3Xq1Q4eCGh0COA
jmfk+8C8u7OJ2PTxM0FECAv3gjmN1qD0vUCPn2OqnT8E0a/gwu+nTxR2nOvAQfU1
MWZasjkd1GQMo1o9MaBALT9E6WIZoFnXQZo/9D2xmv0aoK3vuajAUoA2FOJ+3lAz
Xr9wiECToBrUFJ/tJvGhKciU7Kw2qHYv2d9SkQ==
-----END CERTIFICATE-----