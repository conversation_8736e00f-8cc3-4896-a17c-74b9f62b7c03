# Invoice Access Control Implementation

## Overview

This document describes the implementation of access control for farm-to-farm invoicing, ensuring that invoices received from other farms are read-only for the receiving farm and can only be edited by the farm that sent them.

## Problem Statement

Previously, farms could edit any invoice they had access to, including invoices they received from other farms. This created potential issues where:
- Receiving farms could modify invoice details sent by other farms
- Invoice integrity could be compromised
- Billing disputes could arise from unauthorized modifications

## Solution

Implemented comprehensive access control that restricts invoice operations based on farm ownership:

### Access Control Rules

1. **Sent Invoices** (where `farm_id` matches user's farm):
   - ✅ Can view, edit, delete, cancel, send, and manage documents
   - ✅ Full control over the invoice

2. **Received Invoices** (where `recipient_farm_id` matches user's farm):
   - ✅ Can view the invoice
   - ✅ Can pay the invoice (handled by customer portal)
   - ✅ Can ask questions about the invoice
   - ❌ Cannot edit, delete, cancel, send, or manage documents

3. **Unrelated Invoices** (neither sent nor received by user's farm):
   - ❌ Cannot view or perform any operations

4. **Global Admins**:
   - ✅ Can perform all operations on any invoice

## Implementation Details

### Modified Controller Methods

The following methods in `invoiceController.js` were updated with access control:

1. **`getInvoiceById`** - Added view restrictions and metadata
2. **`updateInvoice`** - Added edit restrictions for received invoices
3. **`deleteInvoice`** - Added delete restrictions for received invoices
4. **`cancelInvoice`** - Added cancel restrictions for received invoices
5. **`sendInvoiceToCustomer`** - Added send restrictions for received invoices
6. **`sendInvoiceReminder`** - Added reminder restrictions for received invoices
7. **`uploadInvoiceDocument`** - Added document upload restrictions for received invoices
8. **`deleteInvoiceDocument`** - Added document deletion restrictions for received invoices

### Access Control Logic

```javascript
// Check if user has permission to edit this invoice
const userFarmId = req.farmId || req.user.activeFarmId;

if (!req.user.is_global_admin) {
  // If this is a received invoice (user's farm is the recipient), deny editing
  if (invoice.recipient_farm_id && invoice.recipient_farm_id === userFarmId) {
    return res.status(403).json({ 
      error: 'You cannot edit invoices received from other farms. Only the sending farm can modify this invoice.' 
    });
  }
  
  // If this is not the user's farm's invoice, deny editing
  if (invoice.farm_id !== userFarmId) {
    return res.status(403).json({ 
      error: 'You can only edit invoices created by your farm.' 
    });
  }
}
```

### Frontend Integration

The `getInvoiceById` method now returns additional metadata to help the frontend:

```javascript
return res.status(200).json({ 
  invoice,
  isReceivedInvoice, // Boolean indicating if this is a received invoice
  canEdit: !isReceivedInvoice || req.user.is_global_admin // Boolean indicating edit permissions
});
```

This allows the frontend to:
- Hide edit buttons for received invoices
- Show appropriate read-only indicators
- Display different UI states based on permissions

## Error Messages

Clear, user-friendly error messages are provided for different scenarios:

- **Received Invoice Edit**: "You cannot edit invoices received from other farms. Only the sending farm can modify this invoice."
- **Received Invoice Delete**: "You cannot delete invoices received from other farms. Only the sending farm can delete this invoice."
- **Received Invoice Cancel**: "You cannot cancel invoices received from other farms. Only the sending farm can cancel this invoice."
- **Not Own Invoice**: "You can only edit invoices created by your farm."
- **View Restriction**: "You can only view invoices that are sent by or received by your farm."

## Testing

Comprehensive tests were implemented in `invoiceAccessControlTest.js` covering:

1. **Access Control Logic**: Verifies that permissions work correctly for different user/farm combinations
2. **Error Messages**: Ensures appropriate error messages are defined
3. **Frontend Metadata**: Validates that the correct metadata is returned for frontend integration

### Test Results

All tests pass, confirming:
- ✅ Received invoices are properly restricted from editing
- ✅ Viewing permissions work correctly
- ✅ Error messages are appropriate
- ✅ Frontend metadata is generated correctly

## Security Considerations

1. **Farm ID Validation**: The system uses `req.farmId` (set by middleware) or `req.user.activeFarmId` to determine the user's current farm context
2. **Global Admin Override**: Global administrators can bypass restrictions for support purposes
3. **Consistent Enforcement**: All invoice modification endpoints enforce the same access control rules
4. **Clear Error Messages**: Users receive clear feedback when access is denied

## Database Schema

The implementation relies on existing database fields:
- `invoices.farm_id`: The farm that created/sent the invoice
- `invoices.recipient_farm_id`: The farm that received the invoice (for farm-to-farm invoicing)
- `invoices.customer_id`: The customer that received the invoice (for farm-to-customer invoicing)

## Future Enhancements

Potential improvements could include:
1. **Audit Logging**: Track access attempts and permission denials
2. **Role-Based Permissions**: More granular permissions based on user roles within farms
3. **Invoice Status Restrictions**: Additional restrictions based on invoice status (e.g., paid invoices)
4. **Notification System**: Notify sending farms when receiving farms view their invoices

## Conclusion

This implementation ensures that farm-to-farm invoicing maintains proper data integrity by preventing receiving farms from modifying invoices sent to them, while still allowing them to view and pay those invoices. The solution is comprehensive, well-tested, and provides clear feedback to users about their permissions.
