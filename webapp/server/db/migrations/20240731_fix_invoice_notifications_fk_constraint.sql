-- Migration: Fix invoice_notifications foreign key constraint to add CASCADE option
-- Depends on:

-- Set search path to site schema
SET search_path TO site;

-- Begin transaction
BEGIN;

-- Drop the existing constraint
ALTER TABLE invoice_notifications DROP CONSTRAINT IF EXISTS invoice_notifications_invoice_id_fkey;

-- Recreate the constraint with ON DELETE CASCADE
ALTER TABLE invoice_notifications 
ADD CONSTRAINT invoice_notifications_invoice_id_fkey 
FOREIGN KEY (invoice_id) 
REFERENCES invoices(id) 
ON DELETE CASCADE;

-- Commit the transaction
COMMIT;