/**
 * Utility functions for managing cookies and sessions
 * This centralizes all cookie-related code to make management easier
 */

import dotenv from 'dotenv';

dotenv.config();

/**
 * Get the cookie domain with proper formatting
 * @returns {string} The cookie domain with leading dot for subdomain support
 */
export const getCookieDomain = () => {
  const mainDomain = process.env.VITE_MAIN_DOMAIN || 'nxtacre.com';
  return `.${mainDomain}`;
};

/**
 * Default cookie options for all cookies
 * @returns {Object} Default cookie options
 */
export const getDefaultCookieOptions = () => {
  return {
    domain: getCookieDomain(),
    secure: true, // Always secure for cross-domain cookies
    httpOnly: true,
    maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days in milliseconds
  };
};

/**
 * Set a secure cookie with consistent options
 * @param {Object} res - Express response object
 * @param {string} name - Cookie name
 * @param {string} value - Cookie value
 * @param {Object} options - Additional cookie options
 * @returns {Object} Express response object
 */
export const setSecureCookie = (res, name, value, options = {}) => {
  return res.cookie(name, value, {
    ...getDefaultCookieOptions(),
    ...options
  });
};

/**
 * Set an authentication token cookie
 * @param {Object} res - Express response object
 * @param {string} token - Authentication token
 * @param {Object} options - Additional cookie options
 * @returns {Object} Express response object
 */
export const setAuthTokenCookie = (res, token, options = {}) => {
  return setSecureCookie(res, 'auth_token', token, options);
};

/**
 * Set a refresh token cookie
 * @param {Object} res - Express response object
 * @param {string} refreshToken - Refresh token
 * @param {Object} options - Additional cookie options
 * @returns {Object} Express response object
 */
export const setRefreshTokenCookie = (res, refreshToken, options = {}) => {
  return setSecureCookie(res, 'refresh_token', refreshToken, options);
};

/**
 * Clear authentication cookies
 * @param {Object} res - Express response object
 * @returns {Object} Express response object
 */
export const clearAuthCookies = (res) => {
  res.clearCookie('auth_token', { domain: getCookieDomain(), secure: true });
  res.clearCookie('refresh_token', { domain: getCookieDomain(), secure: true });
  res.clearCookie('impersonating', { domain: getCookieDomain(), secure: true });
  res.clearCookie('admin_id', { domain: getCookieDomain(), secure: true });
  return res;
};

/**
 * Set impersonation cookies
 * @param {Object} res - Express response object
 * @param {string} adminId - Admin user ID
 * @param {Object} options - Additional cookie options
 * @returns {Object} Express response object
 */
export const setImpersonationCookies = (res, adminId, options = {}) => {
  setSecureCookie(res, 'impersonating', 'true', options);
  setSecureCookie(res, 'admin_id', adminId, options);
  return res;
};

/**
 * Apply cookie middleware to Express app
 * This overrides the default res.cookie function to set consistent options
 * @param {Object} app - Express app
 */
export const applyCookieMiddleware = (app) => {
  app.use((req, res, next) => {
    // Store the original res.cookie function
    const originalCookie = res.cookie;

    // Override the res.cookie function to set default options
    res.cookie = function(name, value, options = {}) {
      // Set default options for all cookies
      const defaultOptions = {
        ...getDefaultCookieOptions(),
        ...options // Allow overriding defaults
      };

      // Always ensure secure is true for cross-domain cookies
      if (!defaultOptions.secure) {
        defaultOptions.secure = true;
      }

      // Call the original cookie function with the new options
      return originalCookie.call(this, name, value, defaultOptions);
    };

    next();
  });
};
