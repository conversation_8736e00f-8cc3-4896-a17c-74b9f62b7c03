import Invoice from '../models/Invoice.js';
import InvoiceItem from '../models/InvoiceItem.js';
import InvoiceQuestion from '../models/InvoiceQuestion.js';
import InvoiceEmail from '../models/InvoiceEmail.js';
import Customer from '../models/Customer.js';
import Farm from '../models/Farm.js';
import CustomerNotification from '../models/CustomerNotification.js';
import User from '../models/User.js';
import { sequelize } from '../config/database.js';
import { updateInventoryOnProductSale } from './productInventoryController.js';
import { sendInvoiceEmail, sendInvoiceReminderEmail, getFrontendUrl } from '../utils/emailUtils.js';
import { verifyInvoiceAuthCode } from '../utils/authUtils.js';
import axios from 'axios';
import { Op } from 'sequelize';
import { PDFDocument, rgb, StandardFonts } from 'pdf-lib';
import fs from 'fs';
import path from 'path';
import InvoiceAuditService from '../services/invoiceAuditService.js';
import InvoiceNotificationService from '../services/invoiceNotificationService.js';
import InvoicePermissionService from '../services/invoicePermissionService.js';

// Function to generate the next sequential invoice number
const generateNextInvoiceNumber = async (farmId) => {
  try {
    // Find the latest invoice for this farm
    const latestInvoice = await Invoice.findOne({
      where: { farm_id: farmId },
      order: [['created_at', 'DESC']]
    });

    if (!latestInvoice) {
      // No invoices exist yet, start with INV-001
      return 'INV-001';
    }

    // Extract the numeric part of the invoice number
    const currentInvoiceNumber = latestInvoice.invoice_number;
    const match = currentInvoiceNumber.match(/^INV-(\d+)$/);

    if (!match) {
      // If the latest invoice doesn't follow our pattern, start a new sequence
      return 'INV-001';
    }

    // Get the numeric part and increment it
    const currentNumber = parseInt(match[1], 10);
    const nextNumber = currentNumber + 1;

    // Format with leading zeros (e.g., INV-001, INV-002, etc.)
    return `INV-${nextNumber.toString().padStart(3, '0')}`;
  } catch (error) {
    console.error('Error generating next invoice number:', error);
    // Fallback to a timestamp-based number if there's an error
    return `INV-${Date.now().toString().slice(-6)}`;
  }
};

// Get all invoices for a farm
export const getFarmInvoices = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get all invoices for the farm (both sent and received)
    const sentInvoices = await Invoice.findAll({
      where: { farm_id: farmId },
      include: [
        {
          model: Customer,
          attributes: ['id', 'name', 'email']
        },
        {
          model: Farm,
          as: 'recipientFarm',
          attributes: ['id', 'name']
        }
      ],
      order: [['issue_date', 'DESC']]
    });

    // Get all invoices where this farm is the recipient
    const receivedInvoices = await Invoice.findAll({
      where: { recipient_farm_id: farmId },
      include: [
        {
          model: Farm,
          attributes: ['id', 'name']
        }
      ],
      order: [['issue_date', 'DESC']]
    });

    // Combine both sets of invoices
    const invoices = [...sentInvoices, ...receivedInvoices];

    return res.status(200).json({ invoices });
  } catch (error) {
    console.error('Error getting farm invoices:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get a single invoice by ID
export const getInvoiceById = async (req, res) => {
  try {
    const { invoiceId } = req.params;

    let invoice;
    try {
      invoice = await Invoice.findByPk(invoiceId, {
        include: [
          {
            model: Customer,
            required: false,
            attributes: ['id', 'name', 'email', 'phone', 'address', 'city', 'state', 'zip_code', 'country']
          },
          {
            model: Farm,
            as: 'recipientFarm',
            required: false,
            attributes: ['id', 'name', 'billing_email', 'phone', 'address', 'city', 'state', 'zip_code', 'country', 'primary_contact_name', 'primary_contact_email', 'primary_contact_phone', 'payment_terms']
          },
          {
            model: Farm,
            required: false,
            attributes: ['id', 'name', 'billing_email', 'phone', 'address', 'city', 'state', 'zip_code', 'country', 'primary_contact_name', 'primary_contact_email', 'primary_contact_phone', 'payment_terms']
          },
          {
            model: InvoiceItem,
            required: false,
            attributes: ['id', 'description', 'quantity', 'unit_price', 'amount', 'taxable']
          }
        ]
      });
    } catch (associationError) {
      console.error('Association error in getInvoiceById:', associationError);

      // Fallback: Get invoice without associations and manually fetch related data
      invoice = await Invoice.findByPk(invoiceId);

      if (invoice) {
        // Manually fetch related data
        if (invoice.customer_id) {
          try {
            invoice.Customer = await Customer.findByPk(invoice.customer_id, {
              attributes: ['id', 'name', 'email', 'phone', 'address', 'city', 'state', 'zip_code', 'country']
            });
          } catch (error) {
            console.error('Error fetching customer:', error);
          }
        }

        if (invoice.farm_id) {
          try {
            invoice.Farm = await Farm.findByPk(invoice.farm_id, {
              attributes: ['id', 'name', 'billing_email', 'phone', 'address', 'city', 'state', 'zip_code', 'country', 'primary_contact_name', 'primary_contact_email', 'primary_contact_phone', 'payment_terms']
            });
          } catch (error) {
            console.error('Error fetching farm:', error);
          }
        }

        if (invoice.recipient_farm_id) {
          try {
            invoice.recipientFarm = await Farm.findByPk(invoice.recipient_farm_id, {
              attributes: ['id', 'name', 'billing_email', 'phone', 'address', 'city', 'state', 'zip_code', 'country', 'primary_contact_name', 'primary_contact_email', 'primary_contact_phone', 'payment_terms']
            });
          } catch (error) {
            console.error('Error fetching recipient farm:', error);
          }
        }

        // Manually fetch invoice items
        try {
          invoice.InvoiceItems = await InvoiceItem.findAll({
            where: { invoice_id: invoiceId },
            attributes: ['id', 'description', 'quantity', 'unit_price', 'amount', 'taxable']
          });
        } catch (error) {
          console.error('Error fetching invoice items:', error);
          invoice.InvoiceItems = [];
        }
      }
    }

    if (!invoice) {
      return res.status(404).json({ error: 'Invoice not found' });
    }

    // Check if user has permission to view this invoice
    // Users can view invoices that are either sent by their farm or received by their farm
    const userFarmId = req.farmId || req.user.activeFarmId;

    if (!req.user.is_global_admin) {
      const canView = (
        invoice.farm_id === userFarmId || // Invoice sent by user's farm
        (invoice.recipient_farm_id && invoice.recipient_farm_id === userFarmId) // Invoice received by user's farm
      );

      if (!canView) {
        return res.status(403).json({
          error: 'You can only view invoices that are sent by or received by your farm.'
        });
      }
    }

    // Log the view action
    // Ensure we have a valid farmId for the audit log
    const auditFarmId = userFarmId || invoice.farm_id || invoice.recipient_farm_id;
    if (auditFarmId) {
      await InvoiceAuditService.logView(
        invoiceId,
        req.user.id,
        auditFarmId,
        req,
        { invoice_number: invoice.invoice_number }
      );
    } else {
      console.error('Unable to log invoice view: No valid farm ID available');
    }

    // Add metadata to indicate if this is a received invoice (read-only for recipient)
    const isReceivedInvoice = invoice.recipient_farm_id && invoice.recipient_farm_id === userFarmId;

    // Send notification if this is a received invoice being viewed for the first time
    if (isReceivedInvoice && invoice.farm_id !== userFarmId) {
      try {
        const viewerFarm = await Farm.findByPk(userFarmId, { attributes: ['id', 'name'] });
        const senderFarm = await Farm.findByPk(invoice.farm_id, { attributes: ['id', 'name'] });

        if (viewerFarm && senderFarm) {
          await InvoiceNotificationService.notifyInvoiceViewed(invoice, viewerFarm, senderFarm);
        }
      } catch (notificationError) {
        console.error('Error sending view notification:', notificationError);
        // Don't fail the request if notification fails
      }
    }

    return res.status(200).json({
      invoice,
      isReceivedInvoice, // Frontend can use this to disable edit controls
      canEdit: !isReceivedInvoice || req.user.is_global_admin
    });
  } catch (error) {
    console.error('Error getting invoice:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get a single invoice by ID with auth code (for unauthenticated access)
export const getInvoiceWithAuthCode = async (req, res) => {
  try {
    const { invoiceId } = req.params;
    const { auth } = req.query;

    // Verify the auth code
    if (!auth) {
      return res.status(401).json({ error: 'Authentication code is required' });
    }

    const decoded = verifyInvoiceAuthCode(auth);
    if (!decoded || decoded.invoiceId !== invoiceId) {
      return res.status(401).json({ error: 'Invalid or expired authentication code' });
    }

    // Get the invoice
    let invoice;
    try {
      invoice = await Invoice.findByPk(invoiceId, {
        include: [
          {
            model: Customer,
            required: false,
            attributes: ['id', 'name', 'email', 'phone', 'address', 'city', 'state', 'zip_code', 'country']
          },
          {
            model: Farm,
            required: false,
            attributes: ['id', 'name', 'billing_email', 'phone', 'address', 'city', 'state', 'zip_code', 'country', 'primary_contact_name', 'primary_contact_email', 'primary_contact_phone', 'payment_terms']
          },
          {
            model: Farm,
            as: 'recipientFarm',
            required: false,
            attributes: ['id', 'name', 'billing_email', 'phone', 'address', 'city', 'state', 'zip_code', 'country', 'primary_contact_name', 'primary_contact_email', 'primary_contact_phone', 'payment_terms']
          },
          {
            model: InvoiceItem,
            required: false,
            attributes: ['id', 'description', 'quantity', 'unit_price', 'amount', 'taxable']
          }
        ]
      });
    } catch (associationError) {
      console.error('Association error in getInvoiceWithAuthCode:', associationError);

      // Fallback: Get invoice without associations and manually fetch related data
      invoice = await Invoice.findByPk(invoiceId);

      if (invoice) {
        // Manually fetch related data
        if (invoice.customer_id) {
          try {
            invoice.Customer = await Customer.findByPk(invoice.customer_id, {
              attributes: ['id', 'name', 'email', 'phone', 'address', 'city', 'state', 'zip_code', 'country']
            });
          } catch (error) {
            console.error('Error fetching customer:', error);
          }
        }

        if (invoice.farm_id) {
          try {
            invoice.Farm = await Farm.findByPk(invoice.farm_id, {
              attributes: ['id', 'name', 'billing_email', 'phone', 'address', 'city', 'state', 'zip_code', 'country', 'primary_contact_name', 'primary_contact_email', 'primary_contact_phone', 'payment_terms']
            });
          } catch (error) {
            console.error('Error fetching farm:', error);
          }
        }

        if (invoice.recipient_farm_id) {
          try {
            invoice.recipientFarm = await Farm.findByPk(invoice.recipient_farm_id, {
              attributes: ['id', 'name', 'billing_email', 'phone', 'address', 'city', 'state', 'zip_code', 'country', 'primary_contact_name', 'primary_contact_email', 'primary_contact_phone', 'payment_terms']
            });
          } catch (error) {
            console.error('Error fetching recipient farm:', error);
          }
        }

        // Manually fetch invoice items
        try {
          invoice.InvoiceItems = await InvoiceItem.findAll({
            where: { invoice_id: invoiceId },
            attributes: ['id', 'description', 'quantity', 'unit_price', 'amount', 'taxable']
          });
        } catch (error) {
          console.error('Error fetching invoice items:', error);
          invoice.InvoiceItems = [];
        }
      }
    }

    if (!invoice) {
      return res.status(404).json({ error: 'Invoice not found' });
    }

    // Verify that the farm ID in the auth code matches the invoice's farm ID
    if (decoded.farmId !== invoice.farm_id.toString()) {
      return res.status(401).json({ error: 'Invalid authentication code for this invoice' });
    }

    // If customerId is in the auth code, verify it matches the invoice's customer ID
    if (decoded.customerId && decoded.customerId !== invoice.customer_id.toString()) {
      return res.status(401).json({ error: 'Invalid authentication code for this customer' });
    }

    // Check if the invoice is in draft or cancelled status
    if ((invoice.status.toLowerCase() === 'draft' || invoice.status.toLowerCase() === 'cancelled')) {
      // If the auth code is for a customer or recipient farm, deny access
      if (decoded.customerId || (decoded.farmId !== invoice.farm_id.toString())) {
        return res.status(403).json({ 
          error: `You cannot view invoices in ${invoice.status} status until they are sent to you.`
        });
      }
    }

    // Add a flag to indicate this is an unauthenticated access
    const responseData = {
      invoice,
      isUnauthenticatedAccess: true,
      accountBenefits: [
        "Save payment methods for faster checkout",
        "View your complete invoice history",
        "Receive notifications about new invoices and payments",
        "Download and print invoices anytime",
        "Contact support directly through your account"
      ]
    };

    return res.status(200).json(responseData);
  } catch (error) {
    console.error('Error getting invoice with auth code:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Create a new invoice
export const createInvoice = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { 
      farmId, 
      customerId,
      recipientFarmId, 
      invoiceNumber, 
      issueDate, 
      dueDate, 
      status, 
      notes,
      items 
    } = req.body;

    // Validate required fields
    if (!farmId) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    // If invoice number is not provided, generate one
    let finalInvoiceNumber = invoiceNumber;
    if (!finalInvoiceNumber) {
      finalInvoiceNumber = await generateNextInvoiceNumber(farmId);
    }

    if (!issueDate) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Issue date is required' });
    }

    if (!dueDate) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Due date is required' });
    }

    if (!items || !Array.isArray(items) || items.length === 0) {
      await transaction.rollback();
      return res.status(400).json({ error: 'At least one invoice item is required' });
    }

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Farm not found' });
    }

    // If customer ID is provided, ensure it exists
    let customerIsTaxExempt = false;
    if (customerId) {
      const customer = await Customer.findOne({
        where: { 
          id: customerId,
          farm_id: farmId
        }
      });

      if (!customer) {
        await transaction.rollback();
        return res.status(404).json({ error: 'Customer not found' });
      }

      // Get the customer's tax exempt status
      customerIsTaxExempt = customer.is_tax_exempt || false;
    }

    // If recipient farm ID is provided, ensure it exists
    if (recipientFarmId) {
      // Check that we're not sending an invoice to ourselves
      if (recipientFarmId === farmId) {
        await transaction.rollback();
        return res.status(400).json({ error: 'Cannot send an invoice to your own farm' });
      }

      const recipientFarm = await Farm.findByPk(recipientFarmId);
      if (!recipientFarm) {
        await transaction.rollback();
        return res.status(404).json({ error: 'Recipient farm not found' });
      }

      // Cannot specify both customer and recipient farm
      if (customerId) {
        await transaction.rollback();
        return res.status(400).json({ error: 'Cannot specify both a customer and a recipient farm' });
      }
    }

    // Calculate subtotal and total
    let subtotal = 0;
    for (const item of items) {
      if (!item.description || !item.quantity || !item.unitPrice) {
        await transaction.rollback();
        return res.status(400).json({ error: 'Each item must have a description, quantity, and unit price' });
      }

      const amount = parseFloat(item.quantity) * parseFloat(item.unitPrice);
      subtotal += amount;
      item.amount = amount;
    }

    const taxAmount = req.body.taxAmount || 0;
    const totalAmount = subtotal + parseFloat(taxAmount);

    // Create invoice
    const invoice = await Invoice.create({
      farm_id: farmId,
      customer_id: customerId,
      recipient_farm_id: recipientFarmId,
      invoice_number: finalInvoiceNumber,
      issue_date: issueDate,
      due_date: dueDate,
      status: status || 'draft',
      subtotal,
      tax_amount: taxAmount,
      total_amount: totalAmount,
      notes
    }, { transaction });

    // Create invoice items
    const invoiceItems = [];
    for (const item of items) {
      // Determine if the item is taxable based on customer's tax exempt status
      // If customer is tax exempt, default to not taxable
      // If taxable is explicitly set in the item, use that value
      const isTaxable = item.taxable !== undefined ? item.taxable : !customerIsTaxExempt;

      const invoiceItem = await InvoiceItem.create({
        invoice_id: invoice.id,
        description: item.description,
        quantity: item.quantity,
        unit_price: item.unitPrice,
        amount: item.amount,
        taxable: isTaxable,
        product_id: item.productId, // Add product_id if provided
        chart_of_account_id: item.chartOfAccountId
      }, { transaction });

      invoiceItems.push(invoiceItem);
    }

    // Update inventory if invoice is paid or sent
    if (status === 'paid' || status === 'sent') {
      try {
        console.log('Updating inventory for invoice items...');
        const insufficientItems = [];

        // First validate all inventory items
        for (const invoiceItem of invoiceItems) {
          if (invoiceItem.product_id) {
            const result = await updateInventoryOnProductSale(invoiceItem, req.user?.id || null, transaction);
            if (!result.success) {
              if (result.insufficientItems) {
                insufficientItems.push(...result.insufficientItems);
              }
            }
          }
        }

        // If there are insufficient items, roll back and return error
        if (insufficientItems.length > 0) {
          await transaction.rollback();
          return res.status(400).json({ 
            error: 'Insufficient inventory for one or more products', 
            insufficientItems 
          });
        }
      } catch (error) {
        console.error('Error updating inventory:', error);
        await transaction.rollback();
        return res.status(500).json({ error: 'Error updating inventory: ' + error.message });
      }
    }

    await transaction.commit();

    // Log the creation
    await InvoiceAuditService.logCreation(
      invoice.id,
      req.user?.id,
      farmId,
      req,
      {
        invoice_number: finalInvoiceNumber,
        total_amount: totalAmount,
        customer_id: customerId,
        recipient_farm_id: recipientFarmId,
        status: status || 'draft'
      }
    );

    // Send notification if this is a farm-to-farm invoice
    if (recipientFarmId) {
      try {
        const senderFarm = await Farm.findByPk(farmId, { attributes: ['id', 'name'] });
        const recipientFarm = await Farm.findByPk(recipientFarmId, { attributes: ['id', 'name'] });

        if (senderFarm && recipientFarm) {
          await InvoiceNotificationService.notifyInvoiceReceived(invoice, senderFarm, recipientFarm);
        }
      } catch (notificationError) {
        console.error('Error sending creation notification:', notificationError);
        // Don't fail the request if notification fails
      }
    }

    return res.status(201).json({
      message: 'Invoice created successfully',
      invoice: {
        ...invoice.toJSON(),
        items: invoiceItems
      }
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating invoice:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update an invoice
export const updateInvoice = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { invoiceId } = req.params;
    const {
      customerId,
      recipientFarmId,
      invoiceNumber,
      issueDate,
      dueDate,
      status,
      notes,
      items,
      taxAmount
    } = req.body;

    // Find invoice to ensure it exists
    const invoice = await Invoice.findByPk(invoiceId, {
      include: [
        {
          model: InvoiceItem,
          required: false
        }
      ]
    });

    if (!invoice) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Invoice not found' });
    }

    // Check if user has permission to edit this invoice using the permission service
    const userFarmId = req.farmId || req.user.activeFarmId;

    const permissionResult = await InvoicePermissionService.checkInvoicePermission({
      userId: req.user.id,
      farmId: userFarmId,
      invoiceId,
      action: 'edit',
      req,
      isGlobalAdmin: req.user.is_global_admin
    });

    if (!permissionResult.allowed) {
      await transaction.rollback();
      return res.status(403).json({
        error: permissionResult.error || 'Permission denied'
      });
    }

    // If customer ID is provided, ensure it exists
    let customerIsTaxExempt = false;
    if (customerId) {
      const customer = await Customer.findOne({
        where: { 
          id: customerId,
          farm_id: invoice.farm_id
        }
      });

      if (!customer) {
        await transaction.rollback();
        return res.status(404).json({ error: 'Customer not found' });
      }

      // Get the customer's tax exempt status
      customerIsTaxExempt = customer.is_tax_exempt || false;
    } else if (invoice.customer_id) {
      // If customerId is not provided but the invoice has a customer_id, get that customer's tax exempt status
      const customer = await Customer.findByPk(invoice.customer_id);
      if (customer) {
        customerIsTaxExempt = customer.is_tax_exempt || false;
      }
    }

    // Update invoice items if provided
    if (items && Array.isArray(items)) {
      // Delete existing items
      await InvoiceItem.destroy({
        where: { invoice_id: invoiceId },
        transaction
      });

      // Calculate new subtotal
      let subtotal = 0;
      for (const item of items) {
        if (!item.description || !item.quantity || !item.unitPrice) {
          await transaction.rollback();
          return res.status(400).json({ error: 'Each item must have a description, quantity, and unit price' });
        }

        const amount = parseFloat(item.quantity) * parseFloat(item.unitPrice);
        subtotal += amount;
        item.amount = amount;
      }

      // Create new items
      const invoiceItems = [];
      for (const item of items) {
        // Determine if the item is taxable based on customer's tax exempt status
        // If customer is tax exempt, default to not taxable
        // If taxable is explicitly set in the item, use that value
        const isTaxable = item.taxable !== undefined ? item.taxable : !customerIsTaxExempt;

        const invoiceItem = await InvoiceItem.create({
          invoice_id: invoice.id,
          description: item.description,
          quantity: item.quantity,
          unit_price: item.unitPrice,
          amount: item.amount,
          taxable: isTaxable,
          product_id: item.productId, // Add product_id if provided
          chart_of_account_id: item.chartOfAccountId
        }, { transaction });

        invoiceItems.push(invoiceItem);
      }

      // Update inventory if invoice is paid or sent
      const newStatus = status || invoice.status;
      if (newStatus === 'paid' || newStatus === 'sent') {
        try {
          console.log('Updating inventory for invoice items...');
          const insufficientItems = [];

          // First validate all inventory items
          for (const invoiceItem of invoiceItems) {
            if (invoiceItem.product_id) {
              const result = await updateInventoryOnProductSale(invoiceItem, req.user?.id || null, transaction);
              if (!result.success) {
                if (result.insufficientItems) {
                  insufficientItems.push(...result.insufficientItems);
                }
              }
            }
          }

          // If there are insufficient items, roll back and return error
          if (insufficientItems.length > 0) {
            await transaction.rollback();
            return res.status(400).json({ 
              error: 'Insufficient inventory for one or more products', 
              insufficientItems 
            });
          }
        } catch (error) {
          console.error('Error updating inventory:', error);
          await transaction.rollback();
          return res.status(500).json({ error: 'Error updating inventory: ' + error.message });
        }
      }

      // Update invoice with new totals
      const newTaxAmount = taxAmount !== undefined ? parseFloat(taxAmount) : invoice.tax_amount;
      const totalAmount = subtotal + newTaxAmount;

      await invoice.update({
        subtotal,
        tax_amount: newTaxAmount,
        total_amount: totalAmount
      }, { transaction });
    }

    // Update other invoice fields
    await invoice.update({
      customer_id: customerId !== undefined ? customerId : invoice.customer_id,
      recipient_farm_id: recipientFarmId !== undefined ? recipientFarmId : invoice.recipient_farm_id,
      invoice_number: invoiceNumber || invoice.invoice_number,
      issue_date: issueDate || invoice.issue_date,
      due_date: dueDate || invoice.due_date,
      status: status || invoice.status,
      notes: notes !== undefined ? notes : invoice.notes
    }, { transaction });

    await transaction.commit();

    // Fetch updated invoice with items
    const updatedInvoice = await Invoice.findByPk(invoiceId, {
      include: [
        {
          model: Customer,
          attributes: ['id', 'name', 'email']
        },
        {
          model: Farm,
          as: 'recipientFarm',
          attributes: ['id', 'name']
        },
        {
          model: InvoiceItem,
          required: false
        }
      ]
    });

    // Log the update after fetching the updated invoice
    // Ensure we have a valid farmId for the audit log
    const auditFarmId = userFarmId || invoice.farm_id || invoice.recipient_farm_id;
    let warning = null;

    if (auditFarmId) {
      try {
        await InvoiceAuditService.logUpdate(
          invoiceId,
          req.user.id,
          auditFarmId,
          req,
          {
            from: {
              status: invoice.status,
              total_amount: invoice.total_amount
            },
            to: {
              status: status || invoice.status,
              total_amount: totalAmount || invoice.total_amount
            }
          },
          {
            invoice_number: invoice.invoice_number,
            updated_fields: Object.keys(req.body)
          }
        );
      } catch (error) {
        console.error('Error logging invoice update:', error);
        warning = 'Invoice updated successfully, but audit logging failed: ' + error.message;
      }
    } else {
      console.error('Unable to log invoice update: No valid farm ID available');
      warning = 'Invoice updated successfully, but audit logging failed due to missing farm ID';
    }

    const response = {
      message: 'Invoice updated successfully',
      invoice: updatedInvoice
    };

    if (warning) {
      response.warning = warning;
    }

    return res.status(200).json(response);
  } catch (error) {
    try {
      // Only attempt to roll back if the transaction is still active
      if (transaction && !transaction.finished) {
        await transaction.rollback();
      }
    } catch (rollbackError) {
      console.error('Error rolling back transaction:', rollbackError);
      // Don't throw this error, as we want to return the original error to the client
    }

    console.error('Error updating invoice:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Cancel an invoice
export const cancelInvoice = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { invoiceId } = req.params;

    // Find invoice to ensure it exists
    const invoice = await Invoice.findByPk(invoiceId);
    if (!invoice) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Invoice not found' });
    }

    // Check if user has permission to cancel this invoice
    // Only the farm that created/sent the invoice can cancel it
    const userFarmId = req.farmId || req.user.activeFarmId;

    if (!req.user.is_global_admin) {
      // If this is a received invoice (user's farm is the recipient), deny cancellation
      if (invoice.recipient_farm_id && invoice.recipient_farm_id === userFarmId) {
        await transaction.rollback();
        return res.status(403).json({
          error: 'You cannot cancel invoices received from other farms. Only the sending farm can cancel this invoice.'
        });
      }

      // If this is not the user's farm's invoice, deny cancellation
      if (invoice.farm_id !== userFarmId) {
        await transaction.rollback();
        return res.status(403).json({
          error: 'You can only cancel invoices created by your farm.'
        });
      }
    }

    // Check if invoice can be cancelled (only draft, sent, or overdue invoices can be cancelled)
    if (invoice.status === 'paid' || invoice.status === 'cancelled') {
      await transaction.rollback();
      return res.status(400).json({
        error: `Invoice cannot be cancelled because it is already ${invoice.status}`
      });
    }

    // Update invoice status to cancelled
    await invoice.update({ 
      status: 'cancelled' 
    }, { transaction });

    await transaction.commit();

    return res.status(200).json({ 
      message: 'Invoice cancelled successfully',
      invoice
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error cancelling invoice:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete an invoice
export const deleteInvoice = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { invoiceId } = req.params;

    // Find invoice to ensure it exists
    const invoice = await Invoice.findByPk(invoiceId);
    if (!invoice) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Invoice not found' });
    }

    // Check if user has permission to delete this invoice
    // Only the farm that created/sent the invoice can delete it
    const userFarmId = req.farmId || req.user.activeFarmId;

    if (!req.user.is_global_admin) {
      // If this is a received invoice (user's farm is the recipient), deny deletion
      if (invoice.recipient_farm_id && invoice.recipient_farm_id === userFarmId) {
        await transaction.rollback();
        return res.status(403).json({
          error: 'You cannot delete invoices received from other farms. Only the sending farm can delete this invoice.'
        });
      }

      // If this is not the user's farm's invoice, deny deletion
      if (invoice.farm_id !== userFarmId) {
        await transaction.rollback();
        return res.status(403).json({
          error: 'You can only delete invoices created by your farm.'
        });
      }
    }

    // Delete invoice (this will cascade delete all related items)
    await invoice.destroy({ transaction });

    await transaction.commit();

    return res.status(200).json({ 
      message: 'Invoice deleted successfully' 
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting invoice:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get the next invoice number for a farm
export const getNextInvoiceNumber = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    const nextInvoiceNumber = await generateNextInvoiceNumber(farmId);
    return res.status(200).json({ nextInvoiceNumber });
  } catch (error) {
    console.error('Error getting next invoice number:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Respond to an invoice question
export const respondToInvoiceQuestion = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { questionId } = req.params;
    const { response, isResolved = true } = req.body;
    const userId = req.user.id;

    // Validate input
    if (!response) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Response is required' });
    }

    // Find the question
    const question = await InvoiceQuestion.findByPk(questionId, {
      include: [
        {
          model: Invoice,
          attributes: ['id', 'invoice_number']
        }
      ]
    });

    if (!question) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Question not found' });
    }

    // Update the question with the response
    await question.update({
      response,
      is_resolved: isResolved,
      responded_at: new Date(),
      responded_by_user_id: userId
    }, { transaction });

    // Get customer details
    const customer = await Customer.findByPk(question.customer_id);
    if (!customer) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Customer not found' });
    }

    // Get farm details
    const farm = await Farm.findByPk(question.farm_id);
    if (!farm) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Create a notification for the customer
    try {
      // Create notification using the API endpoint
      await axios.post(`${process.env.API_BASE_URL || 'http://localhost:3001'}/api/customer/notifications`, {
        title: 'Invoice Question Answered',
        message: `Your question about invoice #${question.Invoice.invoice_number} has been answered.`,
        type: 'info',
        customerId: question.customer_id,
        farmId: question.farm_id,
        relatedEntityType: 'invoice_question',
        relatedEntityId: question.id,
        actionUrl: `/invoices/${question.invoice_id}?tab=questions`
      });
    } catch (notificationError) {
      console.error('Error creating customer notification:', notificationError);
      // Continue with the transaction even if notification creation fails
    }

    await transaction.commit();

    return res.status(200).json({
      message: 'Response added successfully',
      question
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error responding to invoice question:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Send an invoice to a customer via email
export const sendInvoiceToCustomer = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { invoiceId } = req.params;
    const { recipientEmails = [] } = req.body;
    const userId = req.user.id;

    // Find the invoice
    const invoice = await Invoice.findByPk(invoiceId, {
      include: [
        {
          model: Customer,
          attributes: ['id', 'name', 'email']
        },
        {
          model: Farm,
          attributes: ['id', 'name', 'billing_email']
        }
      ]
    });

    if (!invoice) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Invoice not found' });
    }

    // Check if user has permission to send this invoice
    // Only the farm that created/sent the invoice can send it
    const userFarmId = req.farmId || req.user.activeFarmId;

    if (!req.user.is_global_admin) {
      // If this is a received invoice (user's farm is the recipient), deny sending
      if (invoice.recipient_farm_id && invoice.recipient_farm_id === userFarmId) {
        await transaction.rollback();
        return res.status(403).json({
          error: 'You cannot send invoices received from other farms. Only the sending farm can send this invoice.'
        });
      }

      // If this is not the user's farm's invoice, deny sending
      if (invoice.farm_id !== userFarmId) {
        await transaction.rollback();
        return res.status(403).json({
          error: 'You can only send invoices created by your farm.'
        });
      }
    }

    // Get the farm
    const farm = invoice.Farm;
    if (!farm) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get the customer if available
    const customer = invoice.Customer;

    // Get the sender (current user)
    const sender = await User.findByPk(userId);
    if (!sender) {
      await transaction.rollback();
      return res.status(404).json({ error: 'User not found' });
    }

    // Determine the recipient emails
    let emailsToSend = [...recipientEmails];

    // If no additional emails are provided and customer email exists, use the customer's email
    if (emailsToSend.length === 0 && customer?.email) {
      emailsToSend = [customer.email];
    }

    if (emailsToSend.length === 0) {
      await transaction.rollback();
      return res.status(400).json({ error: 'No recipient email provided' });
    }

    // Get the frontend URL
    const frontendUrl = await getFrontendUrl(userId);

    // Send the emails and record them in the database
    const emailResults = [];
    const invoiceEmails = [];

    for (const toEmail of emailsToSend) {
      // Create the invoice email record first to get the tracking ID
      const invoiceEmail = await InvoiceEmail.create({
        invoice_id: invoice.id,
        sent_at: new Date(),
        sent_by_user_id: userId,
        recipient_email: toEmail,
        status: 'sent'
      }, { transaction });

      // Send the email with tracking ID
      const emailResult = await sendInvoiceEmail(invoice, customer, farm, sender, toEmail, frontendUrl, invoiceEmail.tracking_id);
      emailResults.push(emailResult);

      invoiceEmails.push(invoiceEmail);
    }

    // Update the invoice status to 'sent' if it's in 'draft' status
    if (invoice.status === 'draft') {
      await invoice.update({
        status: 'sent'
      }, { transaction });
    }

    await transaction.commit();

    return res.status(200).json({
      message: `Invoice sent successfully to ${emailsToSend.length} recipient(s)`,
      invoiceEmails,
      emailResults
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error sending invoice:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Send a reminder email for an invoice
export const sendInvoiceReminder = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { invoiceId } = req.params;
    const { recipientEmails = [], isOverdue = false } = req.body;
    const userId = req.user.id;

    // Find the invoice
    const invoice = await Invoice.findByPk(invoiceId, {
      include: [
        {
          model: Customer,
          attributes: ['id', 'name', 'email']
        },
        {
          model: Farm,
          attributes: ['id', 'name', 'billing_email']
        }
      ]
    });

    if (!invoice) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Invoice not found' });
    }

    // Check if user has permission to send reminders for this invoice
    // Only the farm that created/sent the invoice can send reminders
    const userFarmId = req.farmId || req.user.activeFarmId;

    if (!req.user.is_global_admin) {
      // If this is a received invoice (user's farm is the recipient), deny sending reminders
      if (invoice.recipient_farm_id && invoice.recipient_farm_id === userFarmId) {
        await transaction.rollback();
        return res.status(403).json({
          error: 'You cannot send reminders for invoices received from other farms. Only the sending farm can send reminders.'
        });
      }

      // If this is not the user's farm's invoice, deny sending reminders
      if (invoice.farm_id !== userFarmId) {
        await transaction.rollback();
        return res.status(403).json({
          error: 'You can only send reminders for invoices created by your farm.'
        });
      }
    }

    // Check if invoice is already paid
    if (invoice.status === 'paid') {
      await transaction.rollback();
      return res.status(400).json({ error: 'Cannot send reminder for a paid invoice' });
    }

    // Get the farm
    const farm = invoice.Farm;
    if (!farm) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get the customer if available
    const customer = invoice.Customer;

    // Get the sender (current user)
    const sender = await User.findByPk(userId);
    if (!sender) {
      await transaction.rollback();
      return res.status(404).json({ error: 'User not found' });
    }

    // Determine the recipient emails
    let emailsToSend = [...recipientEmails];

    // If no additional emails are provided and customer email exists, use the customer's email
    if (emailsToSend.length === 0 && customer?.email) {
      emailsToSend = [customer.email];
    }

    if (emailsToSend.length === 0) {
      await transaction.rollback();
      return res.status(400).json({ error: 'No recipient email provided' });
    }

    // Determine if the invoice is overdue based on due date if not explicitly specified
    let reminderIsOverdue = isOverdue;
    if (!reminderIsOverdue) {
      const dueDate = new Date(invoice.due_date);
      const today = new Date();
      reminderIsOverdue = dueDate < today;
    }

    // Get the frontend URL
    const frontendUrl = await getFrontendUrl(userId);

    // Send the emails and record them in the database
    const emailResults = [];
    const invoiceEmails = [];

    for (const toEmail of emailsToSend) {
      // Create the invoice email record first to get the tracking ID
      const invoiceEmail = await InvoiceEmail.create({
        invoice_id: invoice.id,
        sent_at: new Date(),
        sent_by_user_id: userId,
        recipient_email: toEmail,
        status: 'sent'
      }, { transaction });

      // Send the email with tracking ID
      const emailResult = await sendInvoiceReminderEmail(
        invoice, 
        customer, 
        farm, 
        sender, 
        toEmail, 
        frontendUrl, 
        reminderIsOverdue,
        invoiceEmail.tracking_id
      );
      emailResults.push(emailResult);

      invoiceEmails.push(invoiceEmail);
    }

    await transaction.commit();

    return res.status(200).json({
      message: `Invoice reminder sent successfully to ${emailsToSend.length} recipient(s)`,
      invoiceEmails,
      emailResults
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error sending invoice reminder:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get the email history for an invoice
export const getInvoiceEmailHistory = async (req, res) => {
  try {
    const { invoiceId } = req.params;

    // Find the invoice
    const invoice = await Invoice.findByPk(invoiceId);
    if (!invoice) {
      return res.status(404).json({ error: 'Invoice not found' });
    }

    // Get all emails for this invoice
    const invoiceEmails = await InvoiceEmail.findAll({
      where: { invoice_id: invoiceId },
      include: [
        {
          model: User,
          attributes: ['id', 'first_name', 'last_name', 'email']
        }
      ],
      order: [['sent_at', 'DESC']]
    });

    return res.status(200).json({
      invoiceEmails
    });
  } catch (error) {
    console.error('Error getting invoice email history:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Track when an invoice email is viewed
export const trackInvoiceEmailView = async (req, res) => {
  try {
    const { trackingId } = req.params;

    // Find the invoice email by tracking ID
    const invoiceEmail = await InvoiceEmail.findOne({
      where: { tracking_id: trackingId }
    });

    if (!invoiceEmail) {
      // Return a 1x1 transparent pixel even if tracking ID is not found
      // to avoid revealing that the tracking ID is invalid
      res.set('Content-Type', 'image/gif');
      res.set('Cache-Control', 'no-store, no-cache, must-revalidate, private');
      return res.send(Buffer.from('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7', 'base64'));
    }

    // Update the invoice email status and viewed_at timestamp if not already viewed
    if (!invoiceEmail.viewed_at) {
      await invoiceEmail.update({
        status: 'viewed',
        viewed_at: new Date()
      });
    }

    // Return a 1x1 transparent pixel
    res.set('Content-Type', 'image/gif');
    res.set('Cache-Control', 'no-store, no-cache, must-revalidate, private');
    return res.send(Buffer.from('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7', 'base64'));
  } catch (error) {
    console.error('Error tracking invoice email view:', error);
    // Still return a pixel to avoid revealing errors
    res.set('Content-Type', 'image/gif');
    res.set('Cache-Control', 'no-store, no-cache, must-revalidate, private');
    return res.send(Buffer.from('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7', 'base64'));
  }
};

// Upload a document for an invoice
export const uploadInvoiceDocument = async (req, res) => {
  try {
    const { invoiceId } = req.params;
    const userId = req.user.id;

    // Check if file was uploaded
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    // Find the invoice
    const invoice = await Invoice.findByPk(invoiceId);
    if (!invoice) {
      // No need to clean up files since they're in Digital Ocean Spaces
      return res.status(404).json({ error: 'Invoice not found' });
    }

    // Check if user has permission to upload documents for this invoice
    // Only the farm that created/sent the invoice can upload documents
    const userFarmId = req.farmId || req.user.activeFarmId;

    if (!req.user.is_global_admin) {
      // If this is a received invoice (user's farm is the recipient), deny document upload
      if (invoice.recipient_farm_id && invoice.recipient_farm_id === userFarmId) {
        return res.status(403).json({
          error: 'You cannot upload documents for invoices received from other farms. Only the sending farm can upload documents.'
        });
      }

      // If this is not the user's farm's invoice, deny document upload
      if (invoice.farm_id !== userFarmId) {
        return res.status(403).json({
          error: 'You can only upload documents for invoices created by your farm.'
        });
      }
    }

    // Get file information
    const { originalname, mimetype, size, path: storagePath, spacesUrl } = req.file;

    // Use the URL provided by Digital Ocean Spaces
    const fileUrl = spacesUrl;

    console.log(`File uploaded to Digital Ocean Spaces: ${fileUrl}`);

    // Update the invoice with document information
    await invoice.update({
      document_url: fileUrl,
      document_name: originalname,
      document_type: mimetype,
      document_size: size,
      document_uploaded_at: new Date(),
      document_uploaded_by: userId,
      document_storage_path: storagePath // Store the storage path for future reference
    });

    return res.status(200).json({
      message: 'Document uploaded successfully',
      document: {
        url: fileUrl,
        name: originalname,
        type: mimetype,
        size: size,
        uploaded_at: new Date(),
        uploaded_by: userId
      }
    });
  } catch (error) {
    console.error('Error uploading invoice document:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get the document for an invoice
export const getInvoiceDocument = async (req, res) => {
  try {
    const { invoiceId } = req.params;

    // Import the downloadFromSpaces function
    const { downloadFromSpaces } = await import('../utils/spacesUtils.js');

    // Find the invoice
    const invoice = await Invoice.findByPk(invoiceId);
    if (!invoice) {
      return res.status(404).json({ error: 'Invoice not found' });
    }

    // Check if invoice has a document
    if (!invoice.document_url) {
      return res.status(404).json({ error: 'No document found for this invoice' });
    }

    // Check if we have a storage path (for newer uploads)
    if (invoice.document_storage_path) {
      try {
        // Download the file from Digital Ocean Spaces
        const fileBuffer = await downloadFromSpaces(invoice.document_storage_path);

        // Set appropriate content type
        res.setHeader('Content-Type', invoice.document_type || 'application/octet-stream');
        res.setHeader('Content-Disposition', `inline; filename="${invoice.document_name}"`);

        // Send the file
        return res.send(fileBuffer);
      } catch (downloadError) {
        console.error('Error downloading from Digital Ocean Spaces:', downloadError);
        return res.status(404).json({ 
          error: 'Document file not found in storage. Please try re-uploading the document.'
        });
      }
    } else {
      // For older documents that might have direct URLs to Digital Ocean Spaces

      // If the URL is a full URL (starts with http), redirect to it
      if (invoice.document_url.startsWith('http')) {
        return res.redirect(invoice.document_url);
      }

      // For older documents with local file paths, we need to inform the user
      // that they need to re-upload the document
      return res.status(404).json({ 
        error: 'This document was stored using an older method and is no longer accessible. Please re-upload the document.'
      });
    }
  } catch (error) {
    console.error('Error getting invoice document:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete the document for an invoice
export const deleteInvoiceDocument = async (req, res) => {
  try {
    const { invoiceId } = req.params;

    // Import the deleteFromSpaces function
    const { deleteFromSpaces } = await import('../utils/spacesUtils.js');

    // Find the invoice
    const invoice = await Invoice.findByPk(invoiceId);
    if (!invoice) {
      return res.status(404).json({ error: 'Invoice not found' });
    }

    // Check if user has permission to delete documents for this invoice
    // Only the farm that created/sent the invoice can delete documents
    const userFarmId = req.farmId || req.user.activeFarmId;

    if (!req.user.is_global_admin) {
      // If this is a received invoice (user's farm is the recipient), deny document deletion
      if (invoice.recipient_farm_id && invoice.recipient_farm_id === userFarmId) {
        return res.status(403).json({
          error: 'You cannot delete documents for invoices received from other farms. Only the sending farm can delete documents.'
        });
      }

      // If this is not the user's farm's invoice, deny document deletion
      if (invoice.farm_id !== userFarmId) {
        return res.status(403).json({
          error: 'You can only delete documents for invoices created by your farm.'
        });
      }
    }

    // Check if invoice has a document
    if (!invoice.document_url) {
      return res.status(404).json({ error: 'No document found for this invoice' });
    }

    // Check if we have a storage path (for newer uploads)
    if (invoice.document_storage_path) {
      try {
        // Delete the file from Digital Ocean Spaces
        const deleteResult = await deleteFromSpaces(invoice.document_storage_path);
        if (!deleteResult) {
          console.warn(`Failed to delete file from Digital Ocean Spaces: ${invoice.document_storage_path}`);
          // Continue anyway to update the database
        } else {
          console.log(`Successfully deleted file from Digital Ocean Spaces: ${invoice.document_storage_path}`);
        }
      } catch (deleteError) {
        console.error('Error deleting from Digital Ocean Spaces:', deleteError);
        // Continue anyway to update the database
      }
    } else if (invoice.document_url && !invoice.document_url.startsWith('http')) {
      // For older documents with local file paths, just log a message
      console.log(`Skipping deletion of local file for invoice ${invoiceId} as local storage is no longer supported`);
    }

    // Update the invoice to remove document information
    await invoice.update({
      document_url: null,
      document_name: null,
      document_type: null,
      document_size: null,
      document_uploaded_at: null,
      document_uploaded_by: null,
      document_storage_path: null // Also clear the storage path
    });

    return res.status(200).json({
      message: 'Document deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting invoice document:', error);
    return res.status(500).json({ error: error.message });
  }
};

export const generateInvoicePdf = async (req, res) => {
  try {
    const { invoiceId } = req.params;

    // Find the invoice with all related data
    const invoice = await Invoice.findByPk(invoiceId, {
      include: [
        {
          model: Customer,
          attributes: ['id', 'name', 'email', 'phone', 'address', 'city', 'state', 'zip_code', 'country']
        },
        {
          model: Farm,
          attributes: ['id', 'name', 'billing_email', 'phone', 'address', 'city', 'state', 'zip_code', 'country', 'primary_contact_name', 'primary_contact_email', 'primary_contact_phone', 'payment_terms']
        },
        {
          model: Farm,
          as: 'recipientFarm',
          attributes: ['id', 'name', 'billing_email', 'phone', 'address', 'city', 'state', 'zip_code', 'country', 'primary_contact_name', 'primary_contact_email', 'primary_contact_phone', 'payment_terms']
        },
        {
          model: InvoiceItem,
          required: false,
          attributes: ['id', 'description', 'quantity', 'unit_price', 'amount']
        }
      ]
    });

    if (!invoice) {
      return res.status(404).json({ error: 'Invoice not found' });
    }

    // Create a new PDF document
    const pdfDoc = await PDFDocument.create();

    // Add a page to the document
    const page = pdfDoc.addPage([612, 792]); // Letter size

    // Get fonts
    const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica);
    const helveticaBold = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

    // Set font sizes
    const titleFontSize = 24;
    const headerFontSize = 14;
    const normalFontSize = 12;
    const smallFontSize = 10;

    // Set margins
    const margin = 50;
    const width = page.getWidth() - 2 * margin;

    // Draw invoice header
    page.drawText('INVOICE', {
      x: margin,
      y: 720,
      size: titleFontSize,
      font: helveticaBold,
      color: rgb(0, 0, 0),
    });

    // Draw invoice number and dates
    page.drawText(`Invoice #: ${invoice.invoice_number}`, {
      x: margin,
      y: 690,
      size: normalFontSize,
      font: helveticaFont,
      color: rgb(0, 0, 0),
    });

    page.drawText(`Issue Date: ${new Date(invoice.issue_date).toLocaleDateString()}`, {
      x: margin,
      y: 670,
      size: normalFontSize,
      font: helveticaFont,
      color: rgb(0, 0, 0),
    });

    page.drawText(`Due Date: ${new Date(invoice.due_date).toLocaleDateString()}`, {
      x: margin,
      y: 650,
      size: normalFontSize,
      font: helveticaFont,
      color: rgb(0, 0, 0),
    });

    // Draw status
    const statusText = invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1);
    page.drawText(`Status: ${statusText}`, {
      x: margin,
      y: 630,
      size: normalFontSize,
      font: helveticaBold,
      color: invoice.status === 'paid' ? rgb(0, 0.5, 0) : rgb(0, 0, 0),
    });

    // Draw from (farm) information
    page.drawText('From:', {
      x: margin,
      y: 590,
      size: headerFontSize,
      font: helveticaBold,
      color: rgb(0, 0, 0),
    });

    if (invoice.Farm) {
      page.drawText(invoice.Farm.name, {
        x: margin,
        y: 570,
        size: normalFontSize,
        font: helveticaFont,
        color: rgb(0, 0, 0),
      });

      if (invoice.Farm.address) {
        page.drawText(invoice.Farm.address, {
          x: margin,
          y: 550,
          size: normalFontSize,
          font: helveticaFont,
          color: rgb(0, 0, 0),
        });

        page.drawText(`${invoice.Farm.city}, ${invoice.Farm.state} ${invoice.Farm.zip_code}`, {
          x: margin,
          y: 530,
          size: normalFontSize,
          font: helveticaFont,
          color: rgb(0, 0, 0),
        });
      }

      if (invoice.Farm.billing_email) {
        page.drawText(`Email: ${invoice.Farm.billing_email}`, {
          x: margin,
          y: 510,
          size: normalFontSize,
          font: helveticaFont,
          color: rgb(0, 0, 0),
        });
      }

      if (invoice.Farm.phone) {
        page.drawText(`Phone: ${invoice.Farm.phone}`, {
          x: margin,
          y: 490,
          size: normalFontSize,
          font: helveticaFont,
          color: rgb(0, 0, 0),
        });
      }
    }

    // Draw to (customer or recipient farm) information
    page.drawText('To:', {
      x: 350,
      y: 590,
      size: headerFontSize,
      font: helveticaBold,
      color: rgb(0, 0, 0),
    });

    if (invoice.Customer) {
      page.drawText(invoice.Customer.name, {
        x: 350,
        y: 570,
        size: normalFontSize,
        font: helveticaFont,
        color: rgb(0, 0, 0),
      });

      if (invoice.Customer.address) {
        page.drawText(invoice.Customer.address, {
          x: 350,
          y: 550,
          size: normalFontSize,
          font: helveticaFont,
          color: rgb(0, 0, 0),
        });

        page.drawText(`${invoice.Customer.city}, ${invoice.Customer.state} ${invoice.Customer.zip_code}`, {
          x: 350,
          y: 530,
          size: normalFontSize,
          font: helveticaFont,
          color: rgb(0, 0, 0),
        });
      }

      if (invoice.Customer.email) {
        page.drawText(`Email: ${invoice.Customer.email}`, {
          x: 350,
          y: 510,
          size: normalFontSize,
          font: helveticaFont,
          color: rgb(0, 0, 0),
        });
      }

      if (invoice.Customer.phone) {
        page.drawText(`Phone: ${invoice.Customer.phone}`, {
          x: 350,
          y: 490,
          size: normalFontSize,
          font: helveticaFont,
          color: rgb(0, 0, 0),
        });
      }
    } else if (invoice.recipientFarm) {
      page.drawText(invoice.recipientFarm.name, {
        x: 350,
        y: 570,
        size: normalFontSize,
        font: helveticaFont,
        color: rgb(0, 0, 0),
      });

      if (invoice.recipientFarm.address) {
        page.drawText(invoice.recipientFarm.address, {
          x: 350,
          y: 550,
          size: normalFontSize,
          font: helveticaFont,
          color: rgb(0, 0, 0),
        });

        page.drawText(`${invoice.recipientFarm.city}, ${invoice.recipientFarm.state} ${invoice.recipientFarm.zip_code}`, {
          x: 350,
          y: 530,
          size: normalFontSize,
          font: helveticaFont,
          color: rgb(0, 0, 0),
        });
      }

      if (invoice.recipientFarm.billing_email) {
        page.drawText(`Email: ${invoice.recipientFarm.billing_email}`, {
          x: 350,
          y: 510,
          size: normalFontSize,
          font: helveticaFont,
          color: rgb(0, 0, 0),
        });
      }

      if (invoice.recipientFarm.phone) {
        page.drawText(`Phone: ${invoice.recipientFarm.phone}`, {
          x: 350,
          y: 490,
          size: normalFontSize,
          font: helveticaFont,
          color: rgb(0, 0, 0),
        });
      }
    }

    // Draw invoice items table header
    page.drawText('Description', {
      x: margin,
      y: 440,
      size: headerFontSize,
      font: helveticaBold,
      color: rgb(0, 0, 0),
    });

    page.drawText('Quantity', {
      x: 300,
      y: 440,
      size: headerFontSize,
      font: helveticaBold,
      color: rgb(0, 0, 0),
    });

    page.drawText('Unit Price', {
      x: 400,
      y: 440,
      size: headerFontSize,
      font: helveticaBold,
      color: rgb(0, 0, 0),
    });

    page.drawText('Amount', {
      x: 500,
      y: 440,
      size: headerFontSize,
      font: helveticaBold,
      color: rgb(0, 0, 0),
    });

    // Draw a line under the header
    page.drawLine({
      start: { x: margin, y: 430 },
      end: { x: margin + width, y: 430 },
      thickness: 1,
      color: rgb(0, 0, 0),
    });

    // Draw invoice items
    let y = 410;
    if (invoice.InvoiceItems && invoice.InvoiceItems.length > 0) {
      for (const item of invoice.InvoiceItems) {
        // Format currency
        const unitPrice = new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD'
        }).format(item.unit_price);

        const amount = new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD'
        }).format(item.amount);

        page.drawText(item.description, {
          x: margin,
          y,
          size: normalFontSize,
          font: helveticaFont,
          color: rgb(0, 0, 0),
        });

        page.drawText(item.quantity.toString(), {
          x: 300,
          y,
          size: normalFontSize,
          font: helveticaFont,
          color: rgb(0, 0, 0),
        });

        page.drawText(unitPrice, {
          x: 400,
          y,
          size: normalFontSize,
          font: helveticaFont,
          color: rgb(0, 0, 0),
        });

        page.drawText(amount, {
          x: 500,
          y,
          size: normalFontSize,
          font: helveticaFont,
          color: rgb(0, 0, 0),
        });

        y -= 20;
      }
    }

    // Draw a line above the totals
    page.drawLine({
      start: { x: margin, y: y - 10 },
      end: { x: margin + width, y: y - 10 },
      thickness: 1,
      color: rgb(0, 0, 0),
    });

    // Draw totals
    y -= 30;

    // Format currency
    const subtotal = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(invoice.subtotal);

    const taxAmount = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(invoice.tax_amount);

    const totalAmount = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(invoice.total_amount);

    page.drawText('Subtotal:', {
      x: 400,
      y,
      size: normalFontSize,
      font: helveticaFont,
      color: rgb(0, 0, 0),
    });

    page.drawText(subtotal, {
      x: 500,
      y,
      size: normalFontSize,
      font: helveticaFont,
      color: rgb(0, 0, 0),
    });

    y -= 20;

    page.drawText('Tax:', {
      x: 400,
      y,
      size: normalFontSize,
      font: helveticaFont,
      color: rgb(0, 0, 0),
    });

    page.drawText(taxAmount, {
      x: 500,
      y,
      size: normalFontSize,
      font: helveticaFont,
      color: rgb(0, 0, 0),
    });

    y -= 20;

    page.drawText('Total:', {
      x: 400,
      y,
      size: headerFontSize,
      font: helveticaBold,
      color: rgb(0, 0, 0),
    });

    page.drawText(totalAmount, {
      x: 500,
      y,
      size: headerFontSize,
      font: helveticaBold,
      color: rgb(0, 0, 0),
    });

    // Draw payment information if paid
    if (invoice.status === 'paid' && invoice.payment_date) {
      y -= 40;

      page.drawText('Payment Information', {
        x: margin,
        y,
        size: headerFontSize,
        font: helveticaBold,
        color: rgb(0, 0, 0),
      });

      y -= 20;

      if (invoice.payment_method) {
        const paymentMethod = invoice.payment_method.charAt(0).toUpperCase() + 
                             invoice.payment_method.slice(1).replace('_', ' ');

        page.drawText(`Payment Method: ${paymentMethod}`, {
          x: margin,
          y,
          size: normalFontSize,
          font: helveticaFont,
          color: rgb(0, 0, 0),
        });

        y -= 20;
      }

      page.drawText(`Payment Date: ${new Date(invoice.payment_date).toLocaleDateString()}`, {
        x: margin,
        y,
        size: normalFontSize,
        font: helveticaFont,
        color: rgb(0, 0, 0),
      });

      if (invoice.payment_amount) {
        y -= 20;

        const paymentAmount = new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD'
        }).format(invoice.payment_amount);

        page.drawText(`Payment Amount: ${paymentAmount}`, {
          x: margin,
          y,
          size: normalFontSize,
          font: helveticaFont,
          color: rgb(0, 0, 0),
        });
      }
    }

    // Draw notes if available
    if (invoice.notes) {
      y -= 40;

      page.drawText('Notes:', {
        x: margin,
        y,
        size: headerFontSize,
        font: helveticaBold,
        color: rgb(0, 0, 0),
      });

      y -= 20;

      // Split notes into lines to fit the page width
      const maxWidth = width;
      const words = invoice.notes.split(' ');
      let line = '';

      for (const word of words) {
        const testLine = line + (line ? ' ' : '') + word;
        const testLineWidth = helveticaFont.widthOfTextAtSize(testLine, normalFontSize);

        if (testLineWidth > maxWidth) {
          page.drawText(line, {
            x: margin,
            y,
            size: normalFontSize,
            font: helveticaFont,
            color: rgb(0, 0, 0),
          });

          line = word;
          y -= 20;
        } else {
          line = testLine;
        }
      }

      if (line) {
        page.drawText(line, {
          x: margin,
          y,
          size: normalFontSize,
          font: helveticaFont,
          color: rgb(0, 0, 0),
        });
      }
    }

    // Draw footer
    page.drawText(`Generated on ${new Date().toLocaleDateString()} ${new Date().toLocaleTimeString()}`, {
      x: margin,
      y: 50,
      size: smallFontSize,
      font: helveticaFont,
      color: rgb(0.5, 0.5, 0.5),
    });

    // Serialize the PDF to bytes
    const pdfBytes = await pdfDoc.save();

    // Set response headers
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="Invoice-${invoice.invoice_number}.pdf"`);

    // Send the PDF as the response
    res.send(Buffer.from(pdfBytes));
  } catch (error) {
    console.error('Error generating invoice PDF:', error);
    return res.status(500).json({ error: error.message });
  }
};
