import FarmAssociationPermission from '../models/FarmAssociationPermission.js';
import FarmAssociation from '../models/FarmAssociation.js';
import Farm from '../models/Farm.js';
import User from '../models/User.js';
import UserFarm from '../models/UserFarm.js';
import Role from '../models/Role.js';
import { sequelize } from '../config/database.js';
import { Op } from 'sequelize';
import { sendFarmAssociationPermissionRequestEmail, getFrontendUrl } from '../utils/emailUtils.js';

// Get all permissions for a farm association
export const getPermissionsByAssociation = async (req, res) => {
  try {
    const { associationId } = req.params;

    // Check if association exists
    const association = await FarmAssociation.findByPk(associationId);
    if (!association) {
      return res.status(404).json({ error: 'Farm association not found' });
    }

    // Get all permissions for the association
    const permissions = await FarmAssociationPermission.getPermissionsByAssociation(associationId);

    return res.status(200).json(permissions);
  } catch (error) {
    console.error('Error getting farm association permissions:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get all pending permissions for a farm
export const getPendingPermissionsByFarm = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Check if farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get all pending permissions for the farm
    const permissions = await FarmAssociationPermission.getPendingPermissionsByFarm(farmId);

    return res.status(200).json(permissions);
  } catch (error) {
    console.error('Error getting pending farm association permissions:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Create or update permissions for a farm association
export const updatePermissions = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { associationId } = req.params;
    const { permissions, initiatorFarmId } = req.body;

    // Validate required fields
    if (!permissions || !Array.isArray(permissions)) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Permissions array is required' });
    }

    if (!initiatorFarmId) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Initiator farm ID is required' });
    }

    // Check if association exists
    const association = await FarmAssociation.findByPk(associationId);
    if (!association) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Farm association not found' });
    }

    // Check if association is active
    if (association.status !== 'active') {
      await transaction.rollback();
      return res.status(400).json({ error: 'Cannot update permissions for non-active associations' });
    }

    // Check if initiator farm is part of the association
    if (association.initiator_farm_id !== initiatorFarmId && association.associated_farm_id !== initiatorFarmId) {
      await transaction.rollback();
      return res.status(403).json({ error: 'Initiator farm is not part of this association' });
    }

    // Get the other farm in the association
    const otherFarmId = association.initiator_farm_id === initiatorFarmId 
      ? association.associated_farm_id 
      : association.initiator_farm_id;

    // Process each permission
    const updatedPermissions = [];
    for (const perm of permissions) {
      const { permission_type, status } = perm;

      // Validate permission type and status
      if (!permission_type) {
        await transaction.rollback();
        return res.status(400).json({ error: 'Permission type is required' });
      }

      if (status && !['pending', 'active', 'rejected'].includes(status)) {
        await transaction.rollback();
        return res.status(400).json({ error: 'Invalid status. Must be pending, active, or rejected' });
      }

      // Find existing permission or create new one
      let permission = await FarmAssociationPermission.findOne({
        where: {
          farm_association_id: associationId,
          permission_type
        }
      });

      if (permission) {
        // If permission exists and initiator is changing it, set status to pending
        // unless the initiator is the same as before and status is being set to rejected
        if (permission.initiator_farm_id === initiatorFarmId && status === 'rejected') {
          permission.status = 'rejected';
        } else if (permission.initiator_farm_id !== initiatorFarmId) {
          // If the other farm is changing a permission, set it to active
          permission.status = 'active';
        } else {
          // If the same farm is changing it again, keep it as pending
          permission.status = 'pending';
        }

        permission.initiator_farm_id = initiatorFarmId;
        await permission.save({ transaction });
      } else {
        // Create new permission
        permission = await FarmAssociationPermission.create({
          farm_association_id: associationId,
          permission_type,
          status: 'pending',
          initiator_farm_id: initiatorFarmId
        }, { transaction });
      }

      updatedPermissions.push(permission);
    }

    await transaction.commit();

    // Send email notifications for new or updated permissions with 'pending' status
    try {
      // Get the initiator farm
      const initiatorFarm = await Farm.findByPk(initiatorFarmId);

      // Get the other farm in the association
      const otherFarmId = association.initiator_farm_id === initiatorFarmId 
        ? association.associated_farm_id 
        : association.initiator_farm_id;

      const otherFarm = await Farm.findByPk(otherFarmId);

      if (initiatorFarm && otherFarm) {
        // Find farm administrators of the other farm
        const adminRoles = await Role.findAll({
          where: {
            name: {
              [Op.in]: ['farm_owner', 'farm_admin']
            }
          },
          attributes: ['id']
        });

        const adminRoleIds = adminRoles.map(role => role.id);

        const farmAdmins = await UserFarm.findAll({
          where: {
            farm_id: otherFarmId,
            role_id: {
              [Op.in]: adminRoleIds
            },
            is_approved: true
          },
          include: [
            {
              model: User,
              attributes: ['id', 'first_name', 'last_name', 'email']
            }
          ]
        });

        // Get the frontend URL
        const frontendUrl = await getFrontendUrl(null, otherFarmId);

        // Send email notification to each farm administrator for each pending permission
        for (const admin of farmAdmins) {
          if (admin.User && admin.User.email) {
            for (const permission of updatedPermissions) {
              if (permission.status === 'pending' && permission.initiator_farm_id === initiatorFarmId) {
                await sendFarmAssociationPermissionRequestEmail(
                  initiatorFarm,
                  otherFarm,
                  permission.permission_type,
                  admin.User,
                  frontendUrl
                );
              }
            }
          }
        }
      }
    } catch (emailError) {
      // Log the error but don't fail the request
      console.error('Error sending farm association permission email notifications:', emailError);
    }

    return res.status(200).json(updatedPermissions);
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating farm association permissions:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update a permission status (accept or reject)
export const updatePermissionStatus = async (req, res) => {
  try {
    const { permissionId } = req.params;
    const { status, farmId } = req.body;

    // Validate status
    if (!status || !['active', 'rejected'].includes(status)) {
      return res.status(400).json({ error: 'Invalid status. Must be active or rejected' });
    }

    // Find the permission
    const permission = await FarmAssociationPermission.findByPk(permissionId, {
      include: [
        {
          model: FarmAssociation,
          as: 'farmAssociation'
        }
      ]
    });

    if (!permission) {
      return res.status(404).json({ error: 'Permission not found' });
    }

    // Check if the farm is part of the association
    const association = permission.farmAssociation;
    if (!association) {
      return res.status(404).json({ error: 'Associated farm association not found' });
    }

    // Check if the farm is part of the association
    if (association.initiator_farm_id !== farmId && association.associated_farm_id !== farmId) {
      return res.status(403).json({ error: 'Farm is not part of this association' });
    }

    // Check if the farm is the one that needs to respond (not the initiator)
    if (permission.initiator_farm_id === farmId) {
      return res.status(403).json({ error: 'Cannot update status of your own permission request' });
    }

    // Update the permission
    permission.status = status;
    await permission.save();

    return res.status(200).json(permission);
  } catch (error) {
    console.error('Error updating permission status:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get all permissions for a farm (across all associations)
export const getPermissionsByFarm = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Check if farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get all associations for the farm
    const associations = await FarmAssociation.findAll({
      where: {
        [Op.or]: [
          { initiator_farm_id: farmId },
          { associated_farm_id: farmId }
        ],
        status: 'active'
      }
    });

    const associationIds = associations.map(assoc => assoc.id);

    // Get all permissions for these associations
    const permissions = await FarmAssociationPermission.findAll({
      where: {
        farm_association_id: {
          [Op.in]: associationIds
        }
      },
      include: [
        {
          model: FarmAssociation,
          as: 'farmAssociation',
          include: [
            {
              model: Farm,
              as: 'initiatorFarm',
              attributes: ['id', 'name']
            },
            {
              model: Farm,
              as: 'associatedFarm',
              attributes: ['id', 'name']
            }
          ]
        },
        {
          model: Farm,
          as: 'initiatorFarm',
          attributes: ['id', 'name']
        }
      ]
    });

    return res.status(200).json(permissions);
  } catch (error) {
    console.error('Error getting farm permissions:', error);
    return res.status(500).json({ error: error.message });
  }
};
