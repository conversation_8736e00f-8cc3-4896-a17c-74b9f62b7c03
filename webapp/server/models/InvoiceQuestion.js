import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Invoice from './Invoice.js';
import Farm from './Farm.js';
import dotenv from 'dotenv';

dotenv.config();

const InvoiceQuestion = defineModel('InvoiceQuestion', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  invoice_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Invoice,
      key: 'id'
    }
  },
  customer_id: {
    type: DataTypes.UUID,
    allowNull: false
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  question: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  response: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  is_resolved: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  responded_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  responded_by_user_id: {
    type: DataTypes.UUID,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'invoice_questions',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Define associations
InvoiceQuestion.belongsTo(Invoice, { foreignKey: 'invoice_id' });
Invoice.hasMany(InvoiceQuestion, { foreignKey: 'invoice_id' });

InvoiceQuestion.belongsTo(Farm, { foreignKey: 'farm_id' });
Farm.hasMany(InvoiceQuestion, { foreignKey: 'farm_id' });

// Note: Association with Customer is defined in associations.js

export default InvoiceQuestion;