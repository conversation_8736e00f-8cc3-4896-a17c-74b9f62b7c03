import { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';
import './App.css';
import { useContext, lazy, Suspense, useEffect } from 'react';
import { AuthContext } from './context/AuthContext';
import ThemeWrapper from './components/theme/ThemeWrapper';
import { initReact19CompatibilityCheck } from './utils/react19Compatibility';
import SubdomainAuthCheck from './components/SubdomainAuthCheck';

// Chat components
const ChatWidgetWrapper = lazy(() => import('./components/chat/ChatWidgetWrapper'));

// Notification components
const ToastContainer = lazy(() => import('./components/notifications/ToastContainer'));

// Loading component for Suspense fallback
const PageLoader = () => {
  return (
    <div className="flex items-center justify-center h-screen">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 dark:border-primary-400"></div>
      <p className="ml-3 text-gray-500 dark:text-gray-400">Loading page...</p>
    </div>
  )
}

// Component to redirect based on authentication status
const DefaultRedirect = () => {
  const { user, loading } = useContext(AuthContext)
  const location = useLocation()

  // If still loading, show a loading indicator
  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 dark:border-primary-400"></div>
        <p className="ml-3 text-gray-500 dark:text-gray-400">Loading...</p>
      </div>
    )
  }

  // Redirect to dashboard if logged in, login page if not
  return user 
    ? <Navigate to="/dashboard" replace /> 
    : <Navigate to="/login" state={{ from: location }} replace />
}

// Pages - Using lazy loading for code splitting
// Lazy load error and auth pages as well for better code splitting
const NotFound = lazy(() => import('./pages/NotFound'))
const Unauthorized = lazy(() => import('./pages/Unauthorized'))
const Forbidden = lazy(() => import('./pages/Forbidden'))
const ServerError = lazy(() => import('./pages/ServerError'))
const MaintenancePage = lazy(() => import('./pages/MaintenancePage'))
const Login = lazy(() => import('./pages/Login'))
const Register = lazy(() => import('./pages/Register'))
const VerifyEmail = lazy(() => import('./pages/VerifyEmail'))
const ForgotPassword = lazy(() => import('./pages/ForgotPassword'))
const ResetPassword = lazy(() => import('./pages/ResetPassword'))
const VerifyTwoFactor = lazy(() => import('./pages/VerifyTwoFactor'))
const SetupTwoFactor = lazy(() => import('./pages/SetupTwoFactor'))
// Lazy loaded pages and feature modules
const BusinessRegister = lazy(() => import('./pages/BusinessRegister'))
const Dashboard = lazy(() => import('./pages/Dashboard'))
const FinancialConnectionsLink = lazy(() => import('./pages/FinancialConnectionsLink'))
const Transactions = lazy(() => import('./pages/Transactions'))
const Profile = lazy(() => import('./pages/Profile'))
const BusinessProfile = lazy(() => import('./pages/BusinessProfile'))
const BusinessAccount = lazy(() => import('./pages/BusinessAccount'))
const GlobalAdminRoutes = lazy(() => import('./pages/GlobalAdmin'))
const FAQPage = lazy(() => import('./pages/FAQ'))
const AIAssistant = lazy(() => import('./pages/AIAssistant'))
const CustomDomainPage = lazy(() => import('./pages/CustomDomain'))
const CustomerPortalPage = lazy(() => import('./pages/CustomerPortal'))

// Customer Portal Components
const CustomerLogin = lazy(() => import('./pages/CustomerPortal/customer/Login'))
const CustomerRegister = lazy(() => import('./pages/CustomerPortal/customer/Register'))
const LoginWithToken = lazy(() => import('./pages/CustomerPortal/customer/LoginWithToken'))
const CustomerDashboard = lazy(() => import('./pages/CustomerPortal/customer/Dashboard'))
const CustomerProfile = lazy(() => import('./pages/CustomerPortal/customer/Profile'))
const InvoiceList = lazy(() => import('./pages/CustomerPortal/customer/InvoiceList'))
const CustomerInvoiceDetail = lazy(() => import('./pages/CustomerPortal/customer/InvoiceDetail'))
const InvoicePayment = lazy(() => import('./pages/CustomerPortal/customer/InvoicePayment'))
const PaymentConfirmation = lazy(() => import('./pages/CustomerPortal/customer/PaymentConfirmation'))
const ChangePassword = lazy(() => import('./pages/CustomerPortal/customer/ChangePassword'))
const CustomerForgotPassword = lazy(() => import('./pages/CustomerPortal/customer/ForgotPassword'))
const CustomerResetPassword = lazy(() => import('./pages/CustomerPortal/customer/ResetPassword'))
const CustomerVerifyEmail = lazy(() => import('./pages/CustomerPortal/customer/VerifyEmail'))

// Lazy loaded feature modules and routes
// Feature routes
const CropManagementRoutes = lazy(() => import('./pages/CropManagement'))
const FinancialManagementRoutes = lazy(() => import('./pages/FinancialManagement'))
const VetsRoutes = lazy(() => import('./pages/Vets'))
const IoTRoutes = lazy(() => import('./pages/IoT'))
const AlertsRoutes = lazy(() => import('./pages/Alerts'))
const WorkflowRoutes = lazy(() => import('./pages/Workflows'))
const ReceiptsRoutes = lazy(() => import('./pages/Receipts'))
const BillsRoutes = lazy(() => import('./pages/Bills'))
const SustainabilityRoutes = lazy(() => import('./pages/Sustainability'))
const LaborRoutes = lazy(() => import('./pages/Labor'))
const MarketRoutes = lazy(() => import('./pages/Market'))
const TransportRoutes = lazy(() => import('./pages/Transport'))
const SupportRoutes = lazy(() => import('./pages/Support'))
const MobileFeaturesRoutes = lazy(() => import('./pages/MobileFeatures'))

// Lazy loaded pages
const WeatherPage = lazy(() => import('./pages/Weather'))
const MarketPricesPage = lazy(() => import('./pages/MarketPrices'))
const FieldHealthPage = lazy(() => import('./pages/FieldHealth'))
const DataMigration = lazy(() => import('./pages/Settings/DataMigration'))
const MenuCustomization = lazy(() => import('./pages/Settings/MenuCustomization'))
const RoleManagement = lazy(() => import('./pages/Settings/RoleManagement'))
const PasswordManagerPage = lazy(() => import('./pages/PasswordManager'))
const PasswordGroupDetailPage = lazy(() => import('./pages/PasswordManager/GroupDetailPage'))

// Lazy loaded components - Farms
const FarmsList = lazy(() => import('./pages/Farms/FarmsList'))
const FarmForm = lazy(() => import('./pages/Farms/FarmForm'))
const FarmDetail = lazy(() => import('./pages/Farms/FarmDetail'))
const FarmUserForm = lazy(() => import('./pages/Farms/FarmUserForm'))
const FarmUserEditForm = lazy(() => import('./pages/Farms/FarmUserEditForm'))
const FarmBilling = lazy(() => import('./pages/Farms/FarmBilling'))

// Lazy loaded components - Customers
const CustomersList = lazy(() => import('./pages/Customers/CustomersList'))
const CustomerForm = lazy(() => import('./pages/Customers/CustomerForm'))
const CustomerDetail = lazy(() => import('./pages/Customers/CustomerDetail'))

// Lazy loaded components - Invoices
const InvoicesList = lazy(() => import('./pages/Invoices/InvoicesList'))
const InvoiceForm = lazy(() => import('./pages/Invoices/InvoiceForm'))
const InvoiceDetail = lazy(() => import('./pages/Invoices/InvoiceDetail'))
const PublicInvoiceDetail = lazy(() => import('./pages/Invoices/PublicInvoiceDetail'))

// Billing
const Billing = lazy(() => import('./pages/Billing'))

// Lazy loaded components - Livestock
const LivestockList = lazy(() => import('./pages/Livestock/LivestockList'))
const LivestockForm = lazy(() => import('./pages/Livestock/LivestockForm'))
const LivestockDetail = lazy(() => import('./pages/Livestock/LivestockDetail'))

// Lazy loaded components - Equipment
const EquipmentList = lazy(() => import('./pages/Equipment/EquipmentList'))
const EquipmentForm = lazy(() => import('./pages/Equipment/EquipmentForm'))
const EquipmentDetail = lazy(() => import('./pages/Equipment/EquipmentDetail'))
const EquipmentSharingList = lazy(() => import('./pages/Equipment/EquipmentSharingList'))
const EquipmentSharingForm = lazy(() => import('./pages/Equipment/EquipmentSharingForm'))
const EquipmentSharingDetail = lazy(() => import('./pages/Equipment/EquipmentSharingDetail'))

// Lazy loaded components - Grants
const GrantsList = lazy(() => import('./pages/Grants/GrantsList'))
const GrantDetail = lazy(() => import('./pages/Grants/GrantDetail'))

// Lazy loaded components - Crops
const CropsList = lazy(() => import('./pages/Crops/CropsList'))
const CropForm = lazy(() => import('./pages/Crops/CropForm'))
const CropDetail = lazy(() => import('./pages/Crops/CropDetail'))

// Lazy loaded components - CropTypes
const CropTypesList = lazy(() => import('./pages/CropTypes/CropTypesList'))
const CropTypeForm = lazy(() => import('./pages/CropTypes/CropTypeForm'))
const CropTypeDetail = lazy(() => import('./pages/CropTypes/CropTypeDetail'))

// Lazy loaded components - Products
const ProductsList = lazy(() => import('./pages/Products/ProductsList'))
const ProductForm = lazy(() => import('./pages/Products/ProductForm'))
const ProductDetail = lazy(() => import('./pages/Products/ProductDetail'))
const ProductInventoryManagement = lazy(() => import('./pages/Products/ProductInventoryManagement'))

// Lazy loaded components - Fields
const FieldList = lazy(() => import('./pages/Fields/FieldList'))
const FieldForm = lazy(() => import('./pages/Fields/FieldForm'))
const FieldDetail = lazy(() => import('./pages/Fields/FieldDetail'))
const FieldCollaborationList = lazy(() => import('./pages/FieldCollaborations/FieldCollaborationList'))

// Lazy loaded components - Inventory
const InventoryList = lazy(() => import('./pages/Inventory/InventoryList'))
const InventoryForm = lazy(() => import('./pages/Inventory/InventoryForm'))
const InventoryDetail = lazy(() => import('./pages/Inventory/InventoryDetail'))

// Lazy loaded components - Documents
const DocumentList = lazy(() => import('./pages/Documents/DocumentList'))
const FileManagerPage = lazy(() => import('./pages/Documents/FileManagerPage'))
const FolderList = lazy(() => import('./pages/Documents/FolderList'))
const ExternalStorage = lazy(() => import('./pages/Documents/ExternalStorage'))
const DocumentDetail = lazy(() => import('./pages/Documents/DocumentDetail'))
const DocumentForm = lazy(() => import('./pages/Documents/DocumentForm'))
const FolderForm = lazy(() => import('./pages/Documents/FolderForm'))
const DocumentPermissionPage = lazy(() => import('./pages/Documents/DocumentPermissionPage'))

// Lazy loaded components - Document Signing
const SignableDocumentList = lazy(() => import('./pages/Documents/SignableDocumentList'))
const SignableDocumentForm = lazy(() => import('./pages/Documents/SignableDocumentForm'))
const SignableDocumentDetail = lazy(() => import('./pages/Documents/SignableDocumentDetail'))
const SignableDocumentSigning = lazy(() => import('./pages/Documents/SignableDocumentSigning'))
const SignableDocumentThankYou = lazy(() => import('./pages/Documents/SignableDocumentThankYou'))
const TemplateList = lazy(() => import('./pages/Documents/TemplateList'))
const TemplateForm = lazy(() => import('./pages/Documents/TemplateForm'))
const CreateFromTemplate = lazy(() => import('./pages/Documents/CreateFromTemplate'))
const FormBuilderPage = lazy(() => import('./pages/Documents/FormBuilderPage'))
const CreateFromFormTemplate = lazy(() => import('./pages/Documents/CreateFromFormTemplate'))
const AIDocumentGenerator = lazy(() => import('./pages/Documents/AIDocumentGenerator'))

// Lazy loaded components - Suppliers
const SupplierList = lazy(() => import('./pages/Suppliers/SupplierList'))
const SupplierForm = lazy(() => import('./pages/Suppliers/SupplierForm'))
const SupplierDetail = lazy(() => import('./pages/Suppliers/SupplierDetail'))

// Lazy loaded components - Tasks
const TaskList = lazy(() => import('./pages/Tasks/TaskList'))
const TaskForm = lazy(() => import('./pages/Tasks/TaskForm'))
const TaskDetail = lazy(() => import('./pages/Tasks/TaskDetail'))

// Lazy loaded components - Employees
const EmployeeList = lazy(() => import('./pages/Employees/EmployeeList'))
const EmployeeForm = lazy(() => import('./pages/Employees/EmployeeForm'))
const EmployeeDetail = lazy(() => import('./pages/Employees/EmployeeDetail'))

// Lazy loaded components - Reports
const ReportsList = lazy(() => import('./pages/Reports/ReportsList'))
const ReportDetail = lazy(() => import('./pages/Reports/ReportDetail'))

// Lazy loaded components - Integrations
const IntegrationsList = lazy(() => import('./pages/Integrations/IntegrationsList'))
const IntegrationDetail = lazy(() => import('./pages/Integrations/IntegrationDetail'))
const IntegrationSettings = lazy(() => import('./pages/Integrations/IntegrationSettings'))
const QuickBooksDashboard = lazy(() => import('./pages/Integrations/QuickBooks/QuickBooksDashboard'))
const QuickBooksLink = lazy(() => import('./pages/Integrations/QuickBooks/QuickBooksLink'))
const AmbrookRoutes = lazy(() => import('./pages/Integrations/Ambrook'))

// Lazy loaded components - Subscriptions
const SubscriptionsList = lazy(() => import('./pages/Subscriptions/SubscriptionsList'))
const SubscriptionDetail = lazy(() => import('./pages/Subscriptions/SubscriptionDetail'))

// Lazy loaded components - Soil
const SoilList = lazy(() => import('./pages/Soil/SoilList'))
const SoilDetail = lazy(() => import('./pages/Soil/SoilDetail'))
const SoilForm = lazy(() => import('./pages/Soil/SoilForm'))
const SoilTestResultForm = lazy(() => import('./pages/Soil/SoilTestResultForm'))
const SoilTestResultImport = lazy(() => import('./pages/Soil/SoilTestResultImport'))
const SoilRecommendationForm = lazy(() => import('./pages/Soil/SoilRecommendationForm'))

// Lazy loaded components - Maintenance
const MaintenanceList = lazy(() => import('./pages/Maintenance/MaintenanceList'))
const MaintenanceDetail = lazy(() => import('./pages/Maintenance/MaintenanceDetail'))
const MaintenanceForm = lazy(() => import('./pages/Maintenance/MaintenanceForm'))
const MaintenanceLogForm = lazy(() => import('./pages/Maintenance/MaintenanceLogForm'))

// Lazy loaded components - HR
const HRDashboard = lazy(() => import('./pages/HR'))
const TimeEntriesPage = lazy(() => import('./pages/HR/TimeEntriesPage'))
const NewTimeEntryForm = lazy(() => import('./pages/HR/NewTimeEntryForm'))
const TimeOffRequestsPage = lazy(() => import('./pages/HR/TimeOffRequestsPage'))
const NewTimeOffRequestForm = lazy(() => import('./pages/HR/NewTimeOffRequestForm'))
const PayStubsPage = lazy(() => import('./pages/HR/PayStubsPage'))
const ExpensesPage = lazy(() => import('./pages/HR/ExpensesPage'))
const NewExpenseForm = lazy(() => import('./pages/HR/NewExpenseForm'))

// Components - Lazy load components that aren't needed for initial render
const ProtectedRoute = lazy(() => import('./components/ProtectedRoute'))
const HelpCenter = lazy(() => import('./components/help/HelpCenter'))
const HelpGuideDetail = lazy(() => import('./components/help/HelpGuideDetail'))
import { HelpTipsContainer } from './components/help/HelpTip'

// Context
import { AuthProvider } from './context/AuthContext'
import { FarmProvider } from './context/FarmContext'
import { MenuPreferencesProvider } from './context/MenuPreferencesContext'
import { DashboardProvider } from './context/DashboardContext'
import { HelpProvider } from './context/HelpContext'
import { ChatProvider } from './context/ChatContext'
import { NotificationProvider } from './context/NotificationContext'
import { ThemeProvider } from './context/ThemeContext'
import FeaturePreferenceWrapper from './components/FeaturePreferenceWrapper'
import OnboardingModal from './components/OnboardingModal'

function App() {
  // Initialize React 19 compatibility checking
  useEffect(() => {
    initReact19CompatibilityCheck();
  }, []);

  return (
    <Router>
      <AuthProvider>
        <FarmProvider>
          <MenuPreferencesProvider>
            <DashboardProvider>
              <HelpProvider>
                <NotificationProvider>
                  <ChatProvider>
                    <div className="app overflow-x-hidden">
                      <SubdomainAuthCheck />
                      <Suspense fallback={null}>
                        <ToastContainer />
                      </Suspense>
                      <HelpTipsContainer />
                      <Suspense fallback={null}>
                        <ChatWidgetWrapper />
                      </Suspense>
                      <Routes>
                    {/* Auth Routes */}
                    <Route path="/login" element={
                      <Suspense fallback={<PageLoader />}>
                        <Login />
                      </Suspense>
                    } />
                    <Route path="/register" element={
                      <Suspense fallback={<PageLoader />}>
                        <Register />
                      </Suspense>
                    } />
                    <Route path="/business-register" element={
                      <Suspense fallback={<PageLoader />}>
                        <BusinessRegister />
                      </Suspense>
                    } />
                    <Route path="/forgot-password" element={
                      <Suspense fallback={<PageLoader />}>
                        <ForgotPassword />
                      </Suspense>
                    } />
                    <Route path="/reset-password/:token" element={
                      <Suspense fallback={<PageLoader />}>
                        <ResetPassword />
                      </Suspense>
                    } />
                    <Route path="/verify-2fa" element={
                      <Suspense fallback={<PageLoader />}>
                        <VerifyTwoFactor />
                      </Suspense>
                    } />
                    <Route path="/verify-email/:token" element={
                      <Suspense fallback={<PageLoader />}>
                        <VerifyEmail />
                      </Suspense>
                    } />
                    <Route path="/privacy-policy" element={
                      <Navigate to="https://www.nxtacre.com/privacy-policy" replace />
                    } />
                    <Route path="/terms-and-conditions" element={
                      <Navigate to="https://www.nxtacre.com/terms-and-conditions" replace />
                    } />

                    {/* Protected Routes - All wrapped with Suspense for lazy loading */}
                    <Route element={
                      <Suspense fallback={<PageLoader />}>
                        <ProtectedRoute />
                      </Suspense>
                    }>
                      <Route path="/dashboard" element={<Dashboard />} />
                      <Route path="/link-account" element={<FinancialConnectionsLink />} />
                      <Route path="/transactions" element={<Transactions />} />
                      <Route path="/profile" element={<Profile />} />
                      <Route path="/business-account" element={<BusinessAccount />} />
                      <Route path="/business-profile" element={<BusinessProfile />} />
                      <Route path="/setup-2fa" element={<SetupTwoFactor />} />

                      {/* Help Center Routes */}
                      <Route path="/help" element={
                        <Suspense fallback={<PageLoader />}>
                          <HelpCenter />
                        </Suspense>
                      } />
                      <Route path="/help/guides/:slug" element={
                        <Suspense fallback={<PageLoader />}>
                          <HelpGuideDetail />
                        </Suspense>
                      } />
                      <Route path="/faq" element={<FAQPage />} />

                      {/* Farm Routes */}
                      <Route path="/farms" element={<FarmsList />} />
                      <Route path="/farms/new" element={<FarmForm />} />
                      <Route path="/farms/:farmId" element={<FarmDetail />} />
                      <Route path="/farms/:farmId/edit" element={<FarmForm />} />
                      <Route path="/farms/:farmId/billing" element={<FarmBilling />} />
                      <Route path="/farms/:farmId/custom-domain" element={<CustomDomainPage />} />
                      <Route path="/farms/:farmId/customer-portal" element={<CustomerPortalPage />} />
                      <Route path="/farms/:farmId/users/add" element={<FarmUserForm />} />
                      <Route path="/farms/:farmId/users/:userId/edit" element={<FarmUserEditForm />} />

                      {/* Customer Routes */}
                      <Route path="/customers" element={<CustomersList />} />
                      <Route path="/customers/new" element={<CustomerForm />} />
                      <Route path="/customers/:customerId" element={<CustomerDetail />} />
                      <Route path="/customers/:customerId/edit" element={<CustomerForm />} />

                      {/* Invoice Routes */}
                      <Route path="/invoices" element={<InvoicesList />} />
                      <Route path="/invoices/new" element={<InvoiceForm />} />
                      <Route path="/invoices/:invoiceId" element={<InvoiceDetail />} />
                      <Route path="/invoices/:invoiceId/edit" element={<InvoiceForm />} />

                      {/* Billing Routes */}
                      <Route path="/billing" element={<Billing />} />

                      {/* Receipts Routes */}
                      <Route path="/receipts/*" element={<ReceiptsRoutes />} />

                      {/* Bills Routes */}
                      <Route path="/bills/*" element={<BillsRoutes />} />

                      {/* Product Routes */}
                      <Route path="/products" element={<ProductsList />} />
                      <Route path="/products/new" element={<ProductForm />} />
                      <Route path="/products/:productId" element={<ProductDetail />} />
                      <Route path="/products/:productId/edit" element={<ProductForm />} />
                      <Route path="/products/:productId/inventory" element={<ProductInventoryManagement />} />

                      {/* Other Feature Routes */}
                      <Route path="/livestock" element={<LivestockList />} />
                      <Route path="/livestock/new" element={<LivestockForm />} />
                      <Route path="/livestock/:livestockId" element={<LivestockDetail />} />
                      <Route path="/livestock/:livestockId/edit" element={<LivestockForm />} />

                      <Route path="/equipment" element={<EquipmentList />} />
                      <Route path="/equipment/new" element={<EquipmentForm />} />
                      <Route path="/equipment/:equipmentId" element={<EquipmentDetail />} />
                      <Route path="/equipment/:equipmentId/edit" element={<EquipmentForm />} />

                      {/* Equipment Sharing Routes */}
                      <Route path="/equipment-sharing" element={<EquipmentSharingList />} />
                      <Route path="/equipment-sharing/new" element={<EquipmentSharingForm />} />
                      <Route path="/equipment-sharing/:id" element={<EquipmentSharingDetail />} />
                      <Route path="/equipment-sharing/:id/edit" element={<EquipmentSharingForm />} />

                      <Route path="/crops" element={<CropsList />} />
                      <Route path="/crops/new" element={<CropForm />} />
                      <Route path="/crops/:cropId" element={<CropDetail />} />
                      <Route path="/crops/:cropId/edit" element={<CropForm />} />

                      {/* Crop Type Routes */}
                      <Route path="/crop-types" element={<CropTypesList />} />
                      <Route path="/crop-types/new" element={<CropTypeForm />} />
                      <Route path="/crop-types/:cropTypeId" element={<CropTypeDetail />} />
                      <Route path="/crop-types/:cropTypeId/edit" element={<CropTypeForm />} />

                      {/* Field Routes */}
                      <Route path="/fields" element={<FieldList />} />
                      <Route path="/fields/new" element={<FieldForm />} />
                      <Route path="/fields/:fieldId" element={<FieldDetail />} />
                      <Route path="/fields/:fieldId/edit" element={<FieldForm />} />

                      {/* Field Collaboration Routes */}
                      <Route path="/field-collaborations" element={<FieldCollaborationList />} />

                      {/* Inventory Routes */}
                      <Route path="/inventory" element={<InventoryList />} />
                      <Route path="/inventory/new" element={<InventoryForm />} />
                      <Route path="/inventory/:inventoryId" element={<InventoryDetail />} />
                      <Route path="/inventory/:inventoryId/edit" element={<InventoryForm />} />

                      {/* Supplier Routes */}
                      <Route path="/suppliers" element={<SupplierList />} />
                      <Route path="/suppliers/new" element={<SupplierForm />} />
                      <Route path="/suppliers/:supplierId" element={<SupplierDetail />} />
                      <Route path="/suppliers/:supplierId/edit" element={<SupplierForm />} />

                      {/* Task Routes */}
                      <Route path="/tasks" element={<TaskList />} />
                      <Route path="/tasks/new" element={<TaskForm />} />
                      <Route path="/tasks/:taskId" element={<TaskDetail />} />
                      <Route path="/tasks/:taskId/edit" element={<TaskForm />} />

                      {/* Employee Routes */}
                      <Route path="/employees" element={<EmployeeList />} />
                      <Route path="/employees/new" element={<EmployeeForm />} />
                      <Route path="/employees/:employeeId" element={<EmployeeDetail />} />
                      <Route path="/employees/:employeeId/edit" element={<EmployeeForm />} />

                      {/* Reports Routes */}
                      <Route path="/reports" element={<ReportsList />} />
                      <Route path="/reports/:reportId" element={<ReportDetail />} />

                      {/* Subscription Routes - Redirected to Farm Billing */}
                      <Route path="/subscriptions" element={<Navigate to="/farms" replace />} />
                      <Route path="/subscriptions/:planId" element={<Navigate to="/farms" replace />} />

                      {/* Integration Routes */}
                      <Route path="/integrations" element={<IntegrationsList />} />
                      <Route path="/integrations/:id" element={<IntegrationDetail />} />
                      <Route path="/integrations/:id/settings" element={<IntegrationSettings />} />

                      {/* Soil Management Routes */}
                      <Route path="/soil" element={<SoilList />} />
                      <Route path="/soil/samples/new" element={<SoilForm />} />
                      <Route path="/soil/samples/:sampleId" element={<SoilDetail />} />
                      <Route path="/soil/samples/:sampleId/edit" element={<SoilForm />} />
                      <Route path="/soil/test-results/new" element={<SoilTestResultForm />} />
                      <Route path="/soil/test-results/:resultId/edit" element={<SoilTestResultForm />} />
                      <Route path="/soil/test-results/import/:sampleId" element={<SoilTestResultImport />} />
                      <Route path="/soil/recommendations/new" element={<SoilRecommendationForm />} />
                      <Route path="/soil/recommendations/:recommendationId/edit" element={<SoilRecommendationForm />} />

                      {/* Equipment Maintenance Routes */}
                      <Route path="/maintenance" element={<MaintenanceList />} />
                      <Route path="/maintenance/schedules/new" element={<MaintenanceForm />} />
                      <Route path="/maintenance/schedules/:scheduleId" element={<MaintenanceDetail />} />
                      <Route path="/maintenance/schedules/:scheduleId/edit" element={<MaintenanceForm />} />
                      <Route path="/maintenance/logs/new" element={<MaintenanceLogForm />} />
                      <Route path="/maintenance/logs/:logId/edit" element={<MaintenanceLogForm />} />

                      {/* IoT Device Routes */}
                      <Route path="/iot/*" element={<IoTRoutes />} />

                      {/* Ambrook Integration Routes */}
                      <Route path="/integrations/ambrook/*" element={<AmbrookRoutes />} />

                      {/* Alerts Routes */}
                      <Route path="/alerts/*" element={<AlertsRoutes />} />

                      {/* Workflow Automation Routes */}
                      <Route path="/workflows/*" element={<WorkflowRoutes />} />

                      {/* Veterinary Management Routes */}
                      <Route path="/vets/*" element={<VetsRoutes />} />

                      {/* Weather Routes */}
                      <Route path="/weather" element={<WeatherPage />} />

                      {/* Market Prices Routes */}
                      <Route path="/market-prices" element={<MarketPricesPage />} />

                      {/* Field Health Routes */}
                      <Route path="/field-health" element={<FieldHealthPage />} />

                      {/* Crop Management Routes */}
                      <Route path="/crop-management/*" element={<CropManagementRoutes />} />

                      {/* Financial Management Routes */}
                      <Route path="/financial-management/*" element={<FinancialManagementRoutes />} />

                      {/* Sustainability Routes */}
                      <Route path="/sustainability/*" element={<SustainabilityRoutes />} />

                      {/* Labor Management Routes */}
                      <Route path="/labor/*" element={<LaborRoutes />} />

                      {/* Market Integration Routes */}
                      <Route path="/market/*" element={<MarketRoutes />} />

                      {/* Transport Management Routes */}
                      <Route path="/transport/*" element={<TransportRoutes />} />

                      {/* Support Routes */}
                      <Route path="/support/*" element={<SupportRoutes />} />

                      {/* Mobile Features Routes */}
                      <Route path="/mobile-features/*" element={<MobileFeaturesRoutes />} />

                      {/* AI Assistant Routes */}
                      <Route path="/ai-assistant" element={<AIAssistant />} />

                      {/* QuickBooks Integration Routes */}
                      <Route path="/integrations/quickbooks/link" element={<QuickBooksLink />} />
                      <Route path="/integrations/quickbooks/dashboard" element={<QuickBooksDashboard />} />

                      {/* Agricultural Grants Routes */}
                      <Route path="/grants" element={<GrantsList />} />
                      <Route path="/grants/:grantId" element={<GrantDetail />} />

                      {/* Document Management Routes */}
                      <Route path="/documents" element={<FileManagerPage />} />
                      <Route path="/documents/view/:id" element={<DocumentDetail />} />
                      <Route path="/documents/new" element={<DocumentForm />} />
                      <Route path="/documents/edit/:id" element={<DocumentForm />} />
                      <Route path="/documents/folders" element={<FolderList />} />
                      <Route path="/documents/folders/new" element={<FolderForm />} />
                      <Route path="/documents/folders/edit/:id" element={<FolderForm />} />
                      <Route path="/documents/external" element={<ExternalStorage />} />
                      <Route path="/documents/permissions/:entityType/:entityId" element={<DocumentPermissionPage />} />

                      {/* Document Signing Routes */}
                      <Route path="/documents/signing" element={<SignableDocumentList />} />
                      <Route path="/documents/signing/view/:id" element={<SignableDocumentDetail />} />
                      <Route path="/documents/signing/new" element={<SignableDocumentForm />} />
                      <Route path="/documents/signing/edit/:id" element={<SignableDocumentForm />} />
                      <Route path="/documents/signing/thank-you" element={<SignableDocumentThankYou />} />
                      <Route path="/documents/signing/ai-generator" element={<AIDocumentGenerator />} />

                      {/* Document Template Routes */}
                      <Route path="/documents/signing/templates" element={<TemplateList />} />
                      <Route path="/documents/signing/template/:id" element={<TemplateForm />} />
                      <Route path="/documents/signing/new-template" element={<TemplateForm />} />
                      <Route path="/documents/signing/from-template/:templateId" element={<CreateFromTemplate />} />

                      {/* Form Builder Routes */}
                      <Route path="/documents/signing/form-builder" element={<FormBuilderPage />} />
                      <Route path="/documents/signing/form-builder/:id" element={<FormBuilderPage />} />
                      <Route path="/documents/signing/from-form-template/:templateId" element={<CreateFromFormTemplate />} />

                      {/* Public Document Signing Route (no authentication required) */}
                      <Route path="/sign/:documentId/:signerId" element={<SignableDocumentSigning />} />

                      {/* Settings Routes */}
                      <Route path="/settings/data-migration" element={<DataMigration />} />
                      <Route path="/settings/menu-customization" element={<MenuCustomization />} />
                      <Route path="/settings/roles" element={<RoleManagement />} />

                      {/* HR Routes */}
                      <Route path="/hr" element={<HRDashboard />} />
                      <Route path="/hr/time-entries" element={<TimeEntriesPage />} />
                      <Route path="/hr/time-entries/new" element={<NewTimeEntryForm />} />
                      <Route path="/hr/time-off-requests" element={<TimeOffRequestsPage />} />
                      <Route path="/hr/time-off-requests/new" element={<NewTimeOffRequestForm />} />
                      <Route path="/hr/pay-stubs" element={<PayStubsPage />} />
                      <Route path="/hr/expenses" element={<ExpensesPage />} />
                      <Route path="/hr/expenses/new" element={<NewExpenseForm />} />

                      {/* Global Admin Routes */}
                      <Route path="/admin/*" element={<GlobalAdminRoutes />} />

                      {/* Password Manager Routes */}
                      <Route path="/password-manager" element={<PasswordManagerPage />} />
                      <Route path="/password-manager/groups/new" element={<PasswordManagerPage />} />
                      <Route path="/password-manager/groups/:groupId" element={<PasswordGroupDetailPage />} />
                    </Route>

                    {/* Default Route - redirect to dashboard if logged in, login if not */}
                    <Route path="/" element={
                      <DefaultRedirect />
                    } />

                    {/* Error Routes */}
                    <Route path="/unauthorized" element={
                      <Suspense fallback={<PageLoader />}>
                        <Unauthorized />
                      </Suspense>
                    } />
                    <Route path="/forbidden" element={
                      <Suspense fallback={<PageLoader />}>
                        <Forbidden />
                      </Suspense>
                    } />
                    <Route path="/server-error" element={
                      <Suspense fallback={<PageLoader />}>
                        <ServerError />
                      </Suspense>
                    } />
                    <Route path="/maintenance" element={
                      <Suspense fallback={<PageLoader />}>
                        <MaintenancePage />
                      </Suspense>
                    } />

                    {/* Public Invoice Route (no authentication required) */}
                    <Route path="/invoices/:invoiceId/view" element={
                      <Suspense fallback={<PageLoader />}>
                        <PublicInvoiceDetail />
                      </Suspense>
                    } />

                    {/* Customer Portal Routes */}
                    <Route path="/customer/login/:farmId" element={
                      <Suspense fallback={<PageLoader />}>
                        <CustomerLogin />
                      </Suspense>
                    } />
                    <Route path="/customer/login" element={
                      <Suspense fallback={<PageLoader />}>
                        <CustomerLogin />
                      </Suspense>
                    } />
                    <Route path="/customer/register/:farmId" element={
                      <Suspense fallback={<PageLoader />}>
                        <CustomerRegister />
                      </Suspense>
                    } />
                    <Route path="/customer/register" element={
                      <Suspense fallback={<PageLoader />}>
                        <CustomerRegister />
                      </Suspense>
                    } />
                    <Route path="/customer/login-with-token/:token" element={
                      <Suspense fallback={<PageLoader />}>
                        <LoginWithToken />
                      </Suspense>
                    } />
                    <Route path="/customer/dashboard" element={
                      <Suspense fallback={<PageLoader />}>
                        <CustomerDashboard />
                      </Suspense>
                    } />
                    <Route path="/customer/profile" element={
                      <Suspense fallback={<PageLoader />}>
                        <CustomerProfile />
                      </Suspense>
                    } />
                    <Route path="/customer/invoices" element={
                      <Suspense fallback={<PageLoader />}>
                        <InvoiceList />
                      </Suspense>
                    } />
                    <Route path="/customer/invoices/:invoiceId" element={
                      <Suspense fallback={<PageLoader />}>
                        <CustomerInvoiceDetail />
                      </Suspense>
                    } />
                    <Route path="/customer/invoices/:invoiceId/pay" element={
                      <Suspense fallback={<PageLoader />}>
                        <InvoicePayment />
                      </Suspense>
                    } />
                    <Route path="/customer/payment-confirmation" element={
                      <Suspense fallback={<PageLoader />}>
                        <PaymentConfirmation />
                      </Suspense>
                    } />
                    <Route path="/customer/change-password" element={
                      <Suspense fallback={<PageLoader />}>
                        <ChangePassword />
                      </Suspense>
                    } />
                    <Route path="/customer/forgot-password" element={
                      <Suspense fallback={<PageLoader />}>
                        <CustomerForgotPassword />
                      </Suspense>
                    } />
                    <Route path="/customer/reset-password/:token" element={
                      <Suspense fallback={<PageLoader />}>
                        <CustomerResetPassword />
                      </Suspense>
                    } />
                    <Route path="/customer/verify-email/:token" element={
                      <Suspense fallback={<PageLoader />}>
                        <CustomerVerifyEmail />
                      </Suspense>
                    } />

                    {/* 404 Not Found Route */}
                    <Route path="*" element={
                      <Suspense fallback={<PageLoader />}>
                        <NotFound />
                      </Suspense>
                    } />
                  </Routes>
                </div>
                  </ChatProvider>
                </NotificationProvider>
              </HelpProvider>
            </DashboardProvider>
          </MenuPreferencesProvider>
        </FarmProvider>
      </AuthProvider>
    </Router>
  );
}

export default App;
