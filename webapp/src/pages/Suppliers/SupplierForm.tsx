import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { useAuth } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import { API_URL } from '../../config';
import Layout from '../../components/Layout';
import LocationAutocomplete from '../../components/LocationAutocomplete';
import { loadGoogleMapsApi } from '../../utils/googleMapsLoader';

interface SupplierFormData {
  farm_id: string;
  name: string;
  contact_name: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  website: string;
  description: string;
  payment_terms: string;
  notes: string;
  is_active: boolean;
}

const SupplierForm = () => {
  const { supplierId } = useParams<{ supplierId: string }>();
  const isEditMode = !!supplierId;

  const [formData, setFormData] = useState<SupplierFormData>({
    farm_id: '',
    name: '',
    contact_name: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    country: 'USA',
    website: '',
    description: '',
    payment_terms: '',
    notes: '',
    is_active: true
  });

  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<boolean>(false);
  const [isLoadingMapsApi, setIsLoadingMapsApi] = useState(true);

  const { user } = useAuth();
  const { currentFarm } = useFarm();
  const navigate = useNavigate();

  // Load Google Maps API
  useEffect(() => {
    const loadMapsApi = async () => {
      try {
        await loadGoogleMapsApi(['places']);
        setIsLoadingMapsApi(false);
      } catch (error) {
        console.error('Failed to load Google Maps API:', error);
        setIsLoadingMapsApi(false);
      }
    };

    loadMapsApi();
  }, []);

  useEffect(() => {
    if (currentFarm) {
      setFormData(prev => ({ ...prev, farm_id: currentFarm.id }));
    }
  }, [currentFarm]);

  useEffect(() => {
    if (isEditMode && supplierId) {
      const fetchSupplier = async () => {
        setLoading(true);
        setError(null);

        try {
          const response = await axios.get(`${API_URL}/suppliers/${supplierId}`);
          setFormData(response.data);
        } catch (err) {
          console.error('Error fetching supplier:', err);
          setError('Failed to load supplier data. Please try again later.');
        } finally {
          setLoading(false);
        }
      };

      fetchSupplier();
    }
  }, [supplierId, isEditMode]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  // Handle address selection from Google Places Autocomplete
  const handleAddressSelect = (address: string, coordinates?: { lat: number; lng: number }, addressComponents?: any) => {
    // If we have address components directly from the autocomplete
    if (addressComponents) {
      console.log('Address components received:', addressComponents);

      // Extract address components, with fallbacks
      const streetAddress = addressComponents.streetAddress || '';
      const city = addressComponents.city || '';
      const state = addressComponents.state || '';
      const zipCode = addressComponents.zipCode || '';
      const country = addressComponents.country || 'US';

      // Use streetAddress if available, otherwise use the full address
      const addressToUse = streetAddress || address;

      console.log('Extracted components:', { addressToUse, city, state, zipCode, country });

      // Update form data with extracted components
      setFormData(prev => ({
        ...prev,
        address: addressToUse,
        city: city || prev.city,
        state: state || prev.state,
        zipCode: zipCode || prev.zipCode,
        country: country || prev.country
      }));
    }
    // If we have neither address components nor coordinates, just update the address field
    // This happens when the user types an address manually
    else {
      console.log('Manual address input:', address);
      setFormData(prev => ({
        ...prev,
        address: address
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      if (isEditMode) {
        await axios.put(`${API_URL}/suppliers/${supplierId}`, formData);
      } else {
        await axios.post(`${API_URL}/suppliers`, formData);
      }

      setSuccess(true);
      setTimeout(() => {
        navigate('/suppliers');
      }, 1500);
    } catch (err) {
      console.error('Error saving supplier:', err);
      setError('Failed to save supplier. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-3xl mx-auto">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">
            {isEditMode ? 'Edit Supplier' : 'Add New Supplier'}
          </h1>

          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
              <span className="block sm:inline">{error}</span>
            </div>
          )}

          {success && (
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
              <span className="block sm:inline">Supplier saved successfully! Redirecting...</span>
            </div>
          )}

          <form onSubmit={handleSubmit} className="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
            <div className="mb-4">
              <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="name">
                Supplier Name *
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                required
              />
            </div>

            <div className="mb-4">
              <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="contact_name">
                Contact Person
              </label>
              <input
                type="text"
                id="contact_name"
                name="contact_name"
                value={formData.contact_name}
                onChange={handleChange}
                className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              />
            </div>

            <div className="mb-4 grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="email">
                  Email
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                />
              </div>
              <div>
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="phone">
                  Phone
                </label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                />
              </div>
            </div>

            <div className="mb-4">
              <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="address">
                Street Address
              </label>
              {!isLoadingMapsApi ? (
                <LocationAutocomplete
                  value={formData.address}
                  onChange={handleAddressSelect}
                  placeholder="Enter street address"
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                />
              ) : (
                <input
                  type="text"
                  id="address"
                  name="address"
                  value={formData.address}
                  onChange={handleChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  placeholder="Loading address autocomplete..."
                />
              )}
            </div>

            <div className="mb-4 grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="city">
                  City
                </label>
                <input
                  type="text"
                  id="city"
                  name="city"
                  value={formData.city}
                  onChange={handleChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                />
              </div>
              <div>
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="state">
                  State
                </label>
                <input
                  type="text"
                  id="state"
                  name="state"
                  value={formData.state}
                  onChange={handleChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                />
              </div>
              <div>
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="zipCode">
                  ZIP Code
                </label>
                <input
                  type="text"
                  id="zipCode"
                  name="zipCode"
                  value={formData.zipCode}
                  onChange={handleChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                />
              </div>
            </div>

            <div className="mb-4">
              <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="website">
                Website
              </label>
              <input
                type="url"
                id="website"
                name="website"
                value={formData.website}
                onChange={handleChange}
                className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                placeholder="https://"
              />
            </div>

            <div className="mb-4">
              <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="payment_terms">
                Payment Terms
              </label>
              <input
                type="text"
                id="payment_terms"
                name="payment_terms"
                value={formData.payment_terms}
                onChange={handleChange}
                className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                placeholder="e.g., Net 30, COD"
              />
            </div>

            <div className="mb-4">
              <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="description">
                Description
              </label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                rows={3}
              />
            </div>

            <div className="mb-4">
              <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="notes">
                Notes
              </label>
              <textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleChange}
                className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                rows={3}
              />
            </div>

            <div className="mb-6">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="is_active"
                  name="is_active"
                  checked={formData.is_active}
                  onChange={handleChange}
                  className="mr-2"
                />
                <label className="text-gray-700 text-sm font-bold" htmlFor="is_active">
                  Active
                </label>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <button
                type="button"
                onClick={() => navigate('/suppliers')}
                className="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className={`bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline ${
                  loading ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                {loading ? 'Saving...' : 'Save Supplier'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </Layout>
  );
};

export default SupplierForm;
