import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { format, addDays } from 'date-fns';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { createBill, updateBill, getBillById, getFarmBillCategories } from '../../services/billsService';
import { BillFormData, BillCategory } from '../../types/bills';
import axios from 'axios';
import { API_URL } from '../../config';

interface Vendor {
  id: string;
  name: string;
}

const BillForm = () => {
  const { billId } = useParams<{ billId: string }>();
  const isEditMode = !!billId;
  
  const [formData, setFormData] = useState<BillFormData>({
    farmId: '',
    title: '',
    amount: 0,
    dueDate: format(addDays(new Date(), 30), 'yyyy-MM-dd'),
    status: 'unpaid',
    isRecurring: false
  });

  const [categories, setCategories] = useState<BillCategory[]>([]);
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showRecurringFields, setShowRecurringFields] = useState(false);

  const { selectedFarm } = useFarm();
  const navigate = useNavigate();

  // Set the selected farm when component mounts or selectedFarm changes
  useEffect(() => {
    if (selectedFarm && !isEditMode) {
      setFormData(prev => ({ ...prev, farmId: selectedFarm.id }));
      // Fetch categories and vendors for the selected farm
      fetchCategories(selectedFarm.id);
      fetchVendors(selectedFarm.id);
    }
  }, [selectedFarm, isEditMode]);

  // Fetch categories for a farm
  const fetchCategories = async (farmId: string) => {
    try {
      const categoriesList = await getFarmBillCategories(farmId);
      setCategories(categoriesList);
    } catch (err: any) {
      console.error('Error fetching categories:', err);
    }
  };

  // Fetch vendors for a farm
  const fetchVendors = async (farmId: string) => {
    try {
      const response = await axios.get(`${API_URL}/vendors/farm/${farmId}`);
      const vendorsList = Array.isArray(response.data) ? response.data : response.data.vendors || [];
      setVendors(vendorsList);
    } catch (err: any) {
      console.error('Error fetching vendors:', err);
    }
  };

  // Fetch bill data if in edit mode
  useEffect(() => {
    if (!isEditMode || !billId) return;

    const fetchBill = async () => {
      try {
        setLoading(true);
        setError(null);

        const bill = await getBillById(billId);

        // If we're editing a bill, we need to fetch categories and vendors
        if (bill.farm_id) {
          fetchCategories(bill.farm_id);
          fetchVendors(bill.farm_id);
        }

        setFormData({
          farmId: bill.farm_id,
          categoryId: bill.category_id,
          vendorId: bill.vendor_id,
          title: bill.title,
          description: bill.description,
          amount: bill.amount,
          dueDate: format(new Date(bill.due_date), 'yyyy-MM-dd'),
          status: bill.status,
          paymentMethod: bill.payment_method,
          referenceNumber: bill.reference_number,
          notes: bill.notes,
          isRecurring: bill.is_recurring,
          recurringSchedule: bill.recurringSchedule ? {
            frequency: bill.recurringSchedule.frequency,
            startDate: format(new Date(bill.recurringSchedule.start_date), 'yyyy-MM-dd'),
            endDate: bill.recurringSchedule.end_date ? format(new Date(bill.recurringSchedule.end_date), 'yyyy-MM-dd') : undefined,
            dayOfMonth: bill.recurringSchedule.day_of_month,
            dayOfWeek: bill.recurringSchedule.day_of_week,
            weekOfMonth: bill.recurringSchedule.week_of_month,
            monthOfYear: bill.recurringSchedule.month_of_year,
            nextDueDate: format(new Date(bill.recurringSchedule.next_due_date), 'yyyy-MM-dd')
          } : undefined
        });

        setShowRecurringFields(bill.is_recurring);
      } catch (err: any) {
        console.error('Error fetching bill:', err);
        setError(err.response?.data?.error || 'Failed to load bill details');
      } finally {
        setLoading(false);
      }
    };

    fetchBill();
  }, [billId, isEditMode]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
      
      if (name === 'isRecurring') {
        setShowRecurringFields(checked);
        
        // Initialize recurring schedule if it doesn't exist
        if (checked && !formData.recurringSchedule) {
          setFormData(prev => ({
            ...prev,
            recurringSchedule: {
              frequency: 'monthly',
              startDate: format(new Date(), 'yyyy-MM-dd'),
              nextDueDate: formData.dueDate
            }
          }));
        }
      }
    } else if (name.startsWith('recurringSchedule.')) {
      const field = name.split('.')[1];
      setFormData(prev => ({
        ...prev,
        recurringSchedule: {
          ...prev.recurringSchedule,
          [field]: value
        }
      }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);

    // Validate form
    if (!formData.farmId) {
      setError('Please select a farm');
      setLoading(false);
      return;
    }

    if (!formData.title) {
      setError('Please enter a title');
      setLoading(false);
      return;
    }

    if (!formData.amount || formData.amount <= 0) {
      setError('Please enter a valid amount');
      setLoading(false);
      return;
    }

    if (!formData.dueDate) {
      setError('Please enter a due date');
      setLoading(false);
      return;
    }

    // Validate recurring schedule if bill is recurring
    if (formData.isRecurring) {
      if (!formData.recurringSchedule) {
        setError('Please provide recurring schedule details');
        setLoading(false);
        return;
      }

      if (!formData.recurringSchedule.frequency) {
        setError('Please select a frequency for the recurring bill');
        setLoading(false);
        return;
      }

      if (!formData.recurringSchedule.startDate) {
        setError('Please enter a start date for the recurring bill');
        setLoading(false);
        return;
      }

      if (!formData.recurringSchedule.nextDueDate) {
        setError('Please enter the next due date for the recurring bill');
        setLoading(false);
        return;
      }
    }

    try {
      if (isEditMode) {
        // Update existing bill
        await updateBill(billId!, formData);
        setSuccess('Bill updated successfully');
      } else {
        // Create new bill
        await createBill(formData);
        setSuccess('Bill created successfully');
      }

      // Redirect after a short delay
      setTimeout(() => {
        navigate('/bills');
      }, 2000);
    } catch (err: any) {
      console.error('Error saving bill:', err);
      setError(err.response?.data?.error || 'Failed to save bill');
    } finally {
      setLoading(false);
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          {isEditMode ? 'Edit Bill' : 'Create New Bill'}
        </h1>
        <button
          type="button"
          onClick={() => navigate('/bills')}
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Cancel
        </button>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{success}</span>
        </div>
      )}

      {!selectedFarm ? (
        <div className="bg-white shadow rounded-lg p-8 text-center">
          <svg className="h-16 w-16 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No farm selected</h3>
          <p className="text-gray-500 mb-6">Please select a farm from the header dropdown before creating bills.</p>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
              <h2 className="text-lg font-medium text-gray-900">Bill Details</h2>
            </div>
            <div className="p-6 grid grid-cols-1 gap-6 sm:grid-cols-2">
              {!isEditMode && (
                <div>
                  <label htmlFor="farmId" className="block text-sm font-medium text-gray-700 mb-1">
                    Farm <span className="text-red-500">*</span>
                  </label>
                  <div className="p-2 border rounded bg-gray-50">
                    {selectedFarm ? (
                      <span className="text-gray-700">{selectedFarm.name}</span>
                    ) : (
                      <span className="text-gray-500 italic">Please select a farm from the header dropdown</span>
                    )}
                  </div>
                  <p className="mt-2 text-sm text-gray-500">
                    To change the farm, use the farm selector in the header.
                  </p>
                  <input
                    type="hidden"
                    id="farmId"
                    name="farmId"
                    value={selectedFarm?.id || ''}
                  />
                </div>
              )}

              <div>
                <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                  Title <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  name="title"
                  id="title"
                  required
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  value={formData.title}
                  onChange={handleChange}
                  placeholder="e.g., Electricity Bill"
                />
              </div>

              <div>
                <label htmlFor="categoryId" className="block text-sm font-medium text-gray-700 mb-1">
                  Category
                </label>
                <select
                  id="categoryId"
                  name="categoryId"
                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                  value={formData.categoryId || ''}
                  onChange={handleChange}
                >
                  <option value="">Select a category</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>{category.name}</option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="vendorId" className="block text-sm font-medium text-gray-700 mb-1">
                  Vendor
                </label>
                <select
                  id="vendorId"
                  name="vendorId"
                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                  value={formData.vendorId || ''}
                  onChange={handleChange}
                >
                  <option value="">Select a vendor</option>
                  {vendors.map(vendor => (
                    <option key={vendor.id} value={vendor.id}>{vendor.name}</option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-1">
                  Amount <span className="text-red-500">*</span>
                </label>
                <input
                  type="number"
                  name="amount"
                  id="amount"
                  required
                  min="0.01"
                  step="0.01"
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  value={formData.amount}
                  onChange={handleChange}
                />
              </div>

              <div>
                <label htmlFor="dueDate" className="block text-sm font-medium text-gray-700 mb-1">
                  Due Date <span className="text-red-500">*</span>
                </label>
                <input
                  type="date"
                  name="dueDate"
                  id="dueDate"
                  required
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  value={formData.dueDate}
                  onChange={handleChange}
                />
              </div>

              <div>
                <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  id="status"
                  name="status"
                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                  value={formData.status}
                  onChange={handleChange}
                >
                  <option value="unpaid">Unpaid</option>
                  <option value="paid">Paid</option>
                  <option value="partial">Partial</option>
                  <option value="overdue">Overdue</option>
                </select>
              </div>

              <div>
                <label htmlFor="paymentMethod" className="block text-sm font-medium text-gray-700 mb-1">
                  Payment Method
                </label>
                <input
                  type="text"
                  name="paymentMethod"
                  id="paymentMethod"
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  value={formData.paymentMethod || ''}
                  onChange={handleChange}
                  placeholder="e.g., Credit Card, Check, Cash"
                />
              </div>

              <div>
                <label htmlFor="referenceNumber" className="block text-sm font-medium text-gray-700 mb-1">
                  Reference Number
                </label>
                <input
                  type="text"
                  name="referenceNumber"
                  id="referenceNumber"
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  value={formData.referenceNumber || ''}
                  onChange={handleChange}
                  placeholder="e.g., Invoice #, Account #"
                />
              </div>

              <div className="sm:col-span-2">
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  name="description"
                  id="description"
                  rows={3}
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  value={formData.description || ''}
                  onChange={handleChange}
                  placeholder="Detailed description of the bill..."
                ></textarea>
              </div>

              <div className="sm:col-span-2">
                <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
                  Notes
                </label>
                <textarea
                  name="notes"
                  id="notes"
                  rows={3}
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  value={formData.notes || ''}
                  onChange={handleChange}
                  placeholder="Additional notes or payment instructions..."
                ></textarea>
              </div>

              <div className="sm:col-span-2">
                <div className="flex items-center">
                  <input
                    id="isRecurring"
                    name="isRecurring"
                    type="checkbox"
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    checked={formData.isRecurring}
                    onChange={handleChange}
                  />
                  <label htmlFor="isRecurring" className="ml-2 block text-sm text-gray-900">
                    This is a recurring bill
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* Recurring Bill Schedule */}
          {showRecurringFields && (
            <div className="bg-white shadow rounded-lg overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h2 className="text-lg font-medium text-gray-900">Recurring Schedule</h2>
              </div>
              <div className="p-6 grid grid-cols-1 gap-6 sm:grid-cols-2">
                <div>
                  <label htmlFor="recurringSchedule.frequency" className="block text-sm font-medium text-gray-700 mb-1">
                    Frequency <span className="text-red-500">*</span>
                  </label>
                  <select
                    id="recurringSchedule.frequency"
                    name="recurringSchedule.frequency"
                    required={formData.isRecurring}
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                    value={formData.recurringSchedule?.frequency || ''}
                    onChange={handleChange}
                  >
                    <option value="">Select frequency</option>
                    <option value="weekly">Weekly</option>
                    <option value="biweekly">Biweekly</option>
                    <option value="monthly">Monthly</option>
                    <option value="quarterly">Quarterly</option>
                    <option value="annually">Annually</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="recurringSchedule.startDate" className="block text-sm font-medium text-gray-700 mb-1">
                    Start Date <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="date"
                    name="recurringSchedule.startDate"
                    id="recurringSchedule.startDate"
                    required={formData.isRecurring}
                    className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                    value={formData.recurringSchedule?.startDate || ''}
                    onChange={handleChange}
                  />
                </div>

                <div>
                  <label htmlFor="recurringSchedule.endDate" className="block text-sm font-medium text-gray-700 mb-1">
                    End Date (Optional)
                  </label>
                  <input
                    type="date"
                    name="recurringSchedule.endDate"
                    id="recurringSchedule.endDate"
                    className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                    value={formData.recurringSchedule?.endDate || ''}
                    onChange={handleChange}
                  />
                </div>

                <div>
                  <label htmlFor="recurringSchedule.nextDueDate" className="block text-sm font-medium text-gray-700 mb-1">
                    Next Due Date <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="date"
                    name="recurringSchedule.nextDueDate"
                    id="recurringSchedule.nextDueDate"
                    required={formData.isRecurring}
                    className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                    value={formData.recurringSchedule?.nextDueDate || formData.dueDate}
                    onChange={handleChange}
                  />
                </div>

                {/* Conditional fields based on frequency */}
                {formData.recurringSchedule?.frequency === 'monthly' && (
                  <div>
                    <label htmlFor="recurringSchedule.dayOfMonth" className="block text-sm font-medium text-gray-700 mb-1">
                      Day of Month
                    </label>
                    <input
                      type="number"
                      name="recurringSchedule.dayOfMonth"
                      id="recurringSchedule.dayOfMonth"
                      min="1"
                      max="31"
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                      value={formData.recurringSchedule?.dayOfMonth || ''}
                      onChange={handleChange}
                      placeholder="e.g., 15 for the 15th of each month"
                    />
                  </div>
                )}

                {(formData.recurringSchedule?.frequency === 'weekly' || formData.recurringSchedule?.frequency === 'biweekly') && (
                  <div>
                    <label htmlFor="recurringSchedule.dayOfWeek" className="block text-sm font-medium text-gray-700 mb-1">
                      Day of Week
                    </label>
                    <select
                      id="recurringSchedule.dayOfWeek"
                      name="recurringSchedule.dayOfWeek"
                      className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                      value={formData.recurringSchedule?.dayOfWeek || ''}
                      onChange={handleChange}
                    >
                      <option value="">Select day</option>
                      <option value="0">Sunday</option>
                      <option value="1">Monday</option>
                      <option value="2">Tuesday</option>
                      <option value="3">Wednesday</option>
                      <option value="4">Thursday</option>
                      <option value="5">Friday</option>
                      <option value="6">Saturday</option>
                    </select>
                  </div>
                )}
              </div>
            </div>
          )}

          <div className="flex justify-end">
            <button
              type="submit"
              disabled={loading}
              className={`px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${loading ? 'opacity-70 cursor-not-allowed' : ''}`}
            >
              {loading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {isEditMode ? 'Updating...' : 'Creating...'}
                </>
              ) : (
                isEditMode ? 'Update Bill' : 'Create Bill'
              )}
            </button>
          </div>
        </form>
      )}
    </Layout>
  );
};

export default BillForm;