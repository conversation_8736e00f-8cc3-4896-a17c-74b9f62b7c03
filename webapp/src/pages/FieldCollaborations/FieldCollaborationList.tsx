import { useState, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';

interface FarmFieldCollaboration {
  id: string;
  field_id: string;
  owner_farm_id: string;
  collaborator_farm_id: string;
  permission_level: string;
  created_at: string;
  updated_at: string;
  field?: {
    name: string;
    size: number | null;
    size_unit: string;
    field_type: string;
  };
  ownerFarm?: {
    name: string;
  };
  collaboratorFarm?: {
    name: string;
  };
}

const FieldCollaborationList = () => {
  const [sharedFields, setSharedFields] = useState<FarmFieldCollaboration[]>([]);
  const [collaboratingFields, setCollaboratingFields] = useState<FarmFieldCollaboration[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'shared' | 'collaborating'>('shared');

  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();

  // Fetch field collaborations
  useEffect(() => {
    const fetchFieldCollaborations = async () => {
      setLoading(true);
      setError(null);

      // Only fetch collaborations if a farm is selected
      if (!currentFarm) {
        setSharedFields([]);
        setCollaboratingFields([]);
        setLoading(false);
        return;
      }

      try {
        // First, check if the farm has any active associations with field permissions
        const permissionsResponse = await axios.get(`${API_URL}/farm-association-permissions/farm/${currentFarm.id}`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
          }
        });

        // Filter to only include active equipment permissions (which cover field collaborations)
        const fieldPermissions = permissionsResponse.data.filter(
          (permission: any) => permission.permission_type === 'equipment' && permission.status === 'active'
        );

        // If no active field permissions, don't fetch field collaborations
        if (fieldPermissions.length === 0) {
          setSharedFields([]);
          setCollaboratingFields([]);
          setLoading(false);
          return;
        }

        // Fetch fields shared by this farm
        const sharedResponse = await axios.get(`${API_URL}/field-collaborations/farm/${currentFarm.id}/shared`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
          }
        });
        setSharedFields(sharedResponse.data);

        // Fetch fields shared with this farm
        const collaboratingResponse = await axios.get(`${API_URL}/field-collaborations/farm/${currentFarm.id}/collaborating`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
          }
        });
        setCollaboratingFields(collaboratingResponse.data);
      } catch (err: any) {
        console.error('Error fetching field collaborations:', err);
        setError('Failed to load field collaborations. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchFieldCollaborations();
  }, [currentFarm]);

  // Format permission level for display
  const formatPermissionLevel = (level: string) => {
    if (!level) return 'N/A';
    return level.charAt(0).toUpperCase() + level.slice(1);
  };

  // Get badge class based on permission level
  const getPermissionBadgeClass = (level: string) => {
    switch (level) {
      case 'read':
        return 'bg-blue-100 text-blue-800';
      case 'write':
        return 'bg-green-100 text-green-800';
      case 'admin':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Field Collaborations</h1>
        <Link
          to="/fields"
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Manage Fields
        </Link>
      </div>

      {/* Display selected farm */}
      <div className="mb-6">
        <p className="text-sm text-gray-500">
          Showing collaborations for farm: <span className="font-medium text-gray-700">{currentFarm ? currentFarm.name : 'No Farm Selected'}</span>
        </p>
        {!currentFarm && (
          <p className="text-sm text-gray-500 mt-1">
            Select a farm from the header dropdown to view field collaborations.
          </p>
        )}
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {/* Display message if no active field permissions */}
      {!loading && !error && currentFarm && sharedFields.length === 0 && collaboratingFields.length === 0 && (
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">
            No field collaborations available. You need to have active farm associations with equipment permissions enabled to use field collaborations.
          </span>
        </div>
      )}

      {/* Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('shared')}
            className={`${
              activeTab === 'shared'
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
          >
            Fields You're Sharing
          </button>
          <button
            onClick={() => setActiveTab('collaborating')}
            className={`${
              activeTab === 'collaborating'
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
          >
            Fields Shared With You
          </button>
        </nav>
      </div>

      {loading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
        </div>
      ) : activeTab === 'shared' ? (
        sharedFields.length === 0 ? (
          <div className="bg-white shadow rounded-lg p-6 text-center">
            <p className="text-gray-500">You're not sharing any fields with other farms yet.</p>
            <Link
              to="/fields"
              className="inline-flex items-center px-4 py-2 mt-4 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Manage Your Fields
            </Link>
          </div>
        ) : (
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <ul className="divide-y divide-gray-200">
              {sharedFields.map(collaboration => (
                <li key={collaboration.id}>
                  <div className="block hover:bg-gray-50 px-4 py-4 sm:px-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <p className="text-sm font-medium text-primary-600 truncate">
                          {collaboration.field?.name || 'Unnamed Field'}
                        </p>
                        <span className={`ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getPermissionBadgeClass(collaboration.permission_level)}`}>
                          {formatPermissionLevel(collaboration.permission_level)}
                        </span>
                      </div>
                      <div className="ml-2 flex-shrink-0 flex">
                        <button
                          onClick={() => {
                            // Handle removing collaboration
                            if (window.confirm('Are you sure you want to remove this collaboration?')) {
                              axios.delete(`${API_URL}/field-collaborations/${collaboration.id}`, {
                                headers: {
                                  Authorization: `Bearer ${localStorage.getItem('token')}`
                                }
                              })
                              .then(() => {
                                setSharedFields(sharedFields.filter(c => c.id !== collaboration.id));
                              })
                              .catch(err => {
                                console.error('Error removing collaboration:', err);
                                alert('Failed to remove collaboration. Please try again.');
                              });
                            }
                          }}
                          className="ml-2 inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs font-medium rounded text-red-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                        >
                          Remove
                        </button>
                      </div>
                    </div>
                    <div className="mt-2 sm:flex sm:justify-between">
                      <div className="sm:flex">
                        <p className="flex items-center text-sm text-gray-500">
                          Shared with: {collaboration.collaboratorFarm?.name || 'Unknown Farm'}
                        </p>
                      </div>
                      <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                        <p>
                          Shared since: {new Date(collaboration.created_at).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        )
      ) : (
        collaboratingFields.length === 0 ? (
          <div className="bg-white shadow rounded-lg p-6 text-center">
            <p className="text-gray-500">No fields have been shared with you yet.</p>
          </div>
        ) : (
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <ul className="divide-y divide-gray-200">
              {collaboratingFields.map(collaboration => (
                <li key={collaboration.id}>
                  <Link to={`/fields/${collaboration.field_id}`} className="block hover:bg-gray-50">
                    <div className="px-4 py-4 sm:px-6">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <p className="text-sm font-medium text-primary-600 truncate">
                            {collaboration.field?.name || 'Unnamed Field'}
                          </p>
                          <span className={`ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getPermissionBadgeClass(collaboration.permission_level)}`}>
                            {formatPermissionLevel(collaboration.permission_level)}
                          </span>
                        </div>
                      </div>
                      <div className="mt-2 sm:flex sm:justify-between">
                        <div className="sm:flex">
                          <p className="flex items-center text-sm text-gray-500">
                            Shared by: {collaboration.ownerFarm?.name || 'Unknown Farm'}
                          </p>
                        </div>
                        <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                          <p>
                            Shared since: {new Date(collaboration.created_at).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                    </div>
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        )
      )}
    </Layout>
  );
};

export default FieldCollaborationList;
