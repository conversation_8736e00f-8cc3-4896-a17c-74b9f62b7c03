import React, { useState, useEffect, useRef, MouseEvent } from 'react';
import { useLocation } from 'react-router-dom';
import { useHelp, HelpTip as HelpTipType } from '../../context/HelpContext';
import { XMarkIcon } from '@heroicons/react/24/outline';

interface HelpTipProps {
  tip: HelpTipType;
}

const HelpTip: React.FC<HelpTipProps> = ({ tip }) => {
  const [isVisible, setIsVisible] = useState(false); // Start hidden by default
  const [position, setPosition] = useState<{ top: number; right?: number; left?: number }>({ top: window.innerHeight / 2 - 100, right: 20 }); // Default position in right side middle
  const [hasTargetElement, setHasTargetElement] = useState(false); // Track if we found a target element
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const tipRef = useRef<HTMLDivElement>(null);
  const { dismissTip } = useHelp();

  console.log('Rendering HelpTip:', tip.title);

  useEffect(() => {
    // Set a timeout to ensure the component is rendered before calculating position
    const positionTimeout = setTimeout(() => {
      try {
        // Find the target element using the selector
        if (tip.elementSelector) {
          console.log('Looking for element with selector:', tip.elementSelector);
          const targetElement = document.querySelector(tip.elementSelector);

          if (targetElement) {
            console.log('Found target element for tip:', tip.title);
            setHasTargetElement(true); // We found a target element

            // Calculate position based on the target element and the tip position
            const targetRect = targetElement.getBoundingClientRect();
            const tipRect = tipRef.current?.getBoundingClientRect();

            if (tipRect) {
              let top = 0;
              let left = 0;

              switch (tip.position) {
                case 'top':
                  top = targetRect.top - tipRect.height - 10;
                  left = targetRect.left + (targetRect.width / 2) - (tipRect.width / 2);
                  break;
                case 'right':
                  top = targetRect.top + (targetRect.height / 2) - (tipRect.height / 2);
                  left = targetRect.right + 10;
                  break;
                case 'bottom':
                  top = targetRect.bottom + 10;
                  left = targetRect.left + (targetRect.width / 2) - (tipRect.width / 2);
                  break;
                case 'left':
                  top = targetRect.top + (targetRect.height / 2) - (tipRect.height / 2);
                  left = targetRect.left - tipRect.width - 10;
                  break;
              }

              // Adjust for scroll position
              top += window.scrollY;
              left += window.scrollX;

              // Set position
              console.log('Setting tip position to:', { top, left });
              setPosition({ top, left });

              // Now that we've positioned the tip, make it visible
              setIsVisible(true);
            }
          } else {
            setHasTargetElement(false); // No target element found
            console.log('Target element not found for selector:', tip.elementSelector);
            // If target element not found, position in the top-right corner
            setPosition({ top: 20, right: 20 });

            // Make it visible even when target element not found
            setIsVisible(true);
          }
        } else {
          console.log('No element selector provided, positioning in center');
          setHasTargetElement(false); // No target element since no selector was provided

          // If no selector, position in the center of the viewport
          if (tipRef.current) {
            const tipRect = tipRef.current.getBoundingClientRect();
            const top = (window.innerHeight / 2) - (tipRect.height / 2) + window.scrollY;
            const left = (window.innerWidth / 2) - (tipRect.width / 2) + window.scrollX;

            console.log('Setting tip position to center:', { top, left });
            setPosition({ top, left });

            // Now that we've positioned the tip, make it visible
            setIsVisible(true);
          } else {
            // Fallback position if ref is not available
            setPosition({ top: 20, right: 20 });

            // Make it visible even in fallback case
            setIsVisible(true);
          }
        }
      } catch (error) {
        console.error('Error positioning help tip:', error);
        // Fallback position in case of error
        setHasTargetElement(false);
        setPosition({ top: 20, right: 20 });

        // Make it visible even in error case
        setIsVisible(true);
      }
    }, 100); // Small delay to ensure DOM is ready

    return () => clearTimeout(positionTimeout);
  }, [tip.elementSelector, tip.position, tip.title]);

  const handleDismiss = async () => {
    setIsVisible(false);
    await dismissTip(tip.id);
  };

  // Dragging functionality
  const handleMouseDown = (e: MouseEvent) => {
    if (tipRef.current) {
      setIsDragging(true);
      const rect = tipRef.current.getBoundingClientRect();
      setDragOffset({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      });
    }
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging && tipRef.current) {
      // Calculate new position
      const left = e.clientX - dragOffset.x;
      const top = e.clientY - dragOffset.y;

      // Update position
      setPosition({
        top,
        left
      });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // Add and remove event listeners for dragging
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove as any);
      document.addEventListener('mouseup', handleMouseUp);
    } else {
      document.removeEventListener('mousemove', handleMouseMove as any);
      document.removeEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove as any);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging]);

  // We're using CSS display property to control visibility now, so we don't return null

  return (
    <div
      ref={tipRef}
      className={`fixed z-50 bg-white shadow-lg rounded-lg p-4 w-64 border border-gray-200 transition-opacity duration-300 ${
        isVisible ? 'opacity-100' : 'opacity-0'
      } ${isDragging ? 'cursor-grabbing' : 'cursor-grab'}`}
      style={{
        top: `${position.top}px`,
        ...(position.left !== undefined ? { left: `${position.left}px` } : {}),
        ...(position.right !== undefined ? { right: `${position.right}px` } : {}),
        userSelect: 'none',
        display: 'none', // Always start with display none
        ...(isVisible ? { display: 'block' } : {}), // Only show when isVisible is true
      }}
      onMouseDown={handleMouseDown}
    >
      <div className="flex justify-between items-start mb-2">
        <h3 className="text-sm font-medium text-gray-900">{tip.title}</h3>
        <button
          type="button"
          className="text-gray-400 hover:text-gray-500"
          onClick={handleDismiss}
        >
          <XMarkIcon className="h-4 w-4" />
        </button>
      </div>
      <div 
        className="text-xs text-gray-600"
        dangerouslySetInnerHTML={{ __html: tip.content }}
      />
      <div className="mt-3 flex flex-col space-y-2">
        <div className="flex items-center">
          <input
            type="checkbox"
            id={`disable-all-tips-${tip.id}`}
            className="h-3 w-3 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            onChange={() => useHelp().toggleHelpTipsDisabled()}
            checked={useHelp().helpTipsDisabled}
          />
          <label htmlFor={`disable-all-tips-${tip.id}`} className="ml-2 text-xs text-gray-500">
            Disable all future help tips
          </label>
        </div>
        <div className="flex justify-end">
          <button
            type="button"
            className="text-xs text-primary-600 hover:text-primary-700 font-medium"
            onClick={handleDismiss}
          >
            Got it
          </button>
        </div>
      </div>

      {/* Arrow pointing to the element - only show if we have a target element */}
      {hasTargetElement && (
        <div
          className={`absolute w-3 h-3 bg-white border-t border-l border-gray-200 transform rotate-45 ${
            tip.position === 'top' ? 'bottom-0 translate-y-1.5 left-1/2 -translate-x-1.5' :
            tip.position === 'right' ? 'left-0 -translate-x-1.5 top-1/2 -translate-y-1.5' :
            tip.position === 'bottom' ? 'top-0 -translate-y-1.5 left-1/2 -translate-x-1.5' :
            'right-0 translate-x-1.5 top-1/2 -translate-y-1.5'
          }`}
        />
      )}
    </div>
  );
};

// Container component to manage tips for the current page
export const HelpTipsContainer: React.FC = () => {
  const location = useLocation();
  const { getTipsByPath, tips } = useHelp();
  const [pageTips, setPageTips] = useState<HelpTipType[]>([]);

  useEffect(() => {
    // Get tips for the current page path
    console.log('HelpTipsContainer: Current path:', location.pathname);
    console.log('HelpTipsContainer: All available tips:', tips);

    const pathTips = getTipsByPath(location.pathname);
    console.log('HelpTipsContainer: Tips for current path:', pathTips);

    setPageTips(pathTips);
  }, [location.pathname, getTipsByPath, tips]);

  // Return null if no tips are available for the current path
  if (pageTips.length === 0) {
    console.log('HelpTipsContainer: No tips available for current path');
    return null;
  }

  console.log('HelpTipsContainer: Rendering', pageTips.length, 'tips');

  return (
    <>
      {pageTips.map(tip => (
        <HelpTip key={tip.id} tip={tip} />
      ))}
    </>
  );
};

export default HelpTip;
