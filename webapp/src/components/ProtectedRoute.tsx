import { useContext } from 'react';
import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { AuthContext } from '../context/AuthContext';

const ProtectedRoute = () => {
  const { user, loading } = useContext(AuthContext);
  const location = useLocation();

  // If still loading, show a loading indicator
  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
        <p className="ml-3 text-gray-500">Loading...</p>
      </div>
    );
  }

  // Define paths that should be accessible without authentication
  const publicPaths = [
    '/login',
    '/register',
    '/business-register',
    '/forgot-password',
    '/reset-password',
    '/terms-and-conditions',
    '/privacy-policy'
  ];

  // Check if the current path starts with any of the public paths
  const isPublicPath = publicPaths.some(path => 
    location.pathname === path || 
    location.pathname.startsWith(`${path}/`)
  );

  // If the path is public, allow access without authentication
  if (isPublicPath) {
    return <Outlet />;
  }

  // If not authenticated and not a public path, redirect to login page
  if (!user) {
    // Save the location they were trying to go to
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // If authenticated, render the child routes wrapped in the Layout
  return (
      <Outlet />
  );
};

export default ProtectedRoute;
