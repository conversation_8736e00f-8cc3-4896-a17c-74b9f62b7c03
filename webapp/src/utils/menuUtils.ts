import { API_URL } from '../config';

/**
 * NOTE: This file is the client-side version of menuUtils.js in the server directory.
 * Both files should be kept in sync with each other.
 * The server-side version (menuUtils.js) contains additional functions for database operations.
 * This client-side version (menuUtils.ts) provides TypeScript types and the default menu structure.
 */

// Define types for menu items
export interface MenuItem {
  id: string;
  title: string;
  path: string;
  category: string;
  isRequired: boolean;
  isVisible: boolean;
}

// Define types for menu categories
export interface MenuCategory {
  id: string;
  title: string;
  items: MenuItem[];
}

// Define types for menu preferences
export interface MenuPreferences {
  userId?: string;
  headerItems: MenuItem[];
  sidebarCategories: MenuCategory[];
  quickLinksItems: MenuItem[];
}

/**
 * Returns the default menu structure for the application
 * This is the single source of truth for the menu structure
 * @returns {MenuPreferences} - The default menu structure
 */
export const getDefaultMenuStructure = (): MenuPreferences => {
  // Header items - improved to show key features by default
  // Note: Quick Actions dropdown is hardcoded in the Header component
  const headerItems: MenuItem[] = [
    // Core Operations - always visible by default
    { id: 'dashboard', title: 'Dashboard', path: '/dashboard', category: 'header', isRequired: true, isVisible: true },

    // Key features - visible by default for better discoverability
    { id: 'farm-operations-header', title: 'Farm', path: '/fields', category: 'header', isRequired: false, isVisible: true },
    { id: 'financial-header', title: 'Financial', path: '/transactions', category: 'header', isRequired: false, isVisible: true },
    { id: 'tasks', title: 'Tasks', path: '/tasks', category: 'header', isRequired: false, isVisible: true },

    // Admin - only visible for global admin roles, handled in MenuPreferencesContext
    { id: 'admin-header', title: 'Admin', path: '/admin', category: 'header', isRequired: false, isVisible: false },

    // Other items - hidden by default, can be enabled by user
    { id: 'weather-header', title: 'Weather', path: '/weather', category: 'header', isRequired: false, isVisible: false },
    { id: 'market-prices-header', title: 'Market Prices', path: '/market-prices', category: 'header', isRequired: false, isVisible: false },
    { id: 'sustainability-header', title: 'Sustainability', path: '/sustainability', category: 'header', isRequired: false, isVisible: false },
    { id: 'hr', title: 'HR', path: '/hr', category: 'header', isRequired: false, isVisible: false },
    { id: 'billing-header', title: 'Billing', path: '/billing', category: 'header', isRequired: false, isVisible: true },
    { id: 'labor-header', title: 'Labor', path: '/labor', category: 'header', isRequired: false, isVisible: false },
    { id: 'suppliers-header', title: 'Suppliers', path: '/suppliers', category: 'header', isRequired: false, isVisible: false },
    { id: 'transport-header', title: 'Transport', path: '/transport', category: 'header', isRequired: false, isVisible: false },
    { id: 'market-header', title: 'Market', path: '/market', category: 'header', isRequired: false, isVisible: false },
    { id: 'ai-assistant-header', title: 'AI Assistant', path: '/ai-assistant', category: 'header', isRequired: false, isVisible: false },
  ];

  // Sidebar categories - reorganized with improved logical grouping
  const sidebarCategories: MenuCategory[] = [
    // Core Farm Management
    {
      id: 'dashboard-analytics',
      title: 'Dashboard & Analytics',
      items: [
        { id: 'dashboard-sidebar', title: 'Dashboard', path: '/dashboard', category: 'dashboard-analytics', isRequired: false, isVisible: true },
        { id: 'reports', title: 'Reports', path: '/reports', category: 'dashboard-analytics', isRequired: false, isVisible: true },
        { id: 'market-prices', title: 'Market Price Tracker', path: '/market-prices', category: 'dashboard-analytics', isRequired: false, isVisible: true },
        { id: 'field-health', title: 'Field Health Analytics', path: '/field-health', category: 'dashboard-analytics', isRequired: false, isVisible: true },
      ]
    },
    {
      id: 'farm-operations',
      title: 'Farm Operations',
      items: [
        { id: 'fields', title: 'Fields', path: '/fields', category: 'farm-operations', isRequired: false, isVisible: true },
        { id: 'field-collaborations', title: 'Field Collaborations', path: '/field-collaborations', category: 'farm-operations', isRequired: false, isVisible: true },
        { id: 'crops', title: 'Crops', path: '/crops', category: 'farm-operations', isRequired: false, isVisible: true },
        { id: 'crop-types', title: 'Crop Types', path: '/crop-types', category: 'farm-operations', isRequired: false, isVisible: true },
        { id: 'livestock', title: 'Livestock', path: '/livestock', category: 'farm-operations', isRequired: false, isVisible: true },
        { id: 'soil', title: 'Soil Management', path: '/soil', category: 'farm-operations', isRequired: false, isVisible: true },
        { id: 'vets', title: 'Veterinary Management', path: '/vets', category: 'farm-operations', isRequired: false, isVisible: true },
        { id: 'weather', title: 'Weather System', path: '/weather', category: 'farm-operations', isRequired: false, isVisible: true },
      ]
    },
    {
      id: 'crop-management',
      title: 'Crop Management',
      items: [
        { id: 'crop-disease-prediction', title: 'Disease Prediction', path: '/crop-management/disease-prediction', category: 'crop-management', isRequired: false, isVisible: true },
        { id: 'yield-prediction', title: 'Yield Prediction', path: '/crop-management/yield-prediction', category: 'crop-management', isRequired: false, isVisible: true },
        { id: 'crop-rotation', title: 'Rotation Optimization', path: '/crop-management/rotation-optimization', category: 'crop-management', isRequired: false, isVisible: true },
        { id: 'harvest-scheduling', title: 'Harvest Scheduling', path: '/crop-management/harvest-scheduling', category: 'crop-management', isRequired: false, isVisible: true },
      ]
    },
    {
      id: 'tasks-workflows',
      title: 'Tasks & Workflows',
      items: [
        { id: 'tasks-sidebar', title: 'Tasks', path: '/tasks', category: 'tasks-workflows', isRequired: false, isVisible: true },
        { id: 'workflows', title: 'Workflow Automation', path: '/workflows', category: 'tasks-workflows', isRequired: false, isVisible: true },
        { id: 'alerts', title: 'Alerts & Notifications', path: '/alerts', category: 'tasks-workflows', isRequired: false, isVisible: true },
      ]
    },

    // Financial & Business Operations
    {
      id: 'financial-management',
      title: 'Financial Management',
      items: [
        { id: 'transactions', title: 'Transactions', path: '/transactions', category: 'financial-management', isRequired: false, isVisible: true },
        { id: 'link-account', title: 'Link Account', path: '/link-account', category: 'financial-management', isRequired: false, isVisible: true },
        { id: 'invoices', title: 'Invoices', path: '/invoices', category: 'financial-management', isRequired: false, isVisible: true },
        { id: 'receipts', title: 'Receipts', path: '/receipts', category: 'financial-management', isRequired: false, isVisible: true },
        { id: 'bills', title: 'Bills', path: '/bills', category: 'financial-management', isRequired: false, isVisible: true },
        { id: 'billing', title: 'Billing', path: '/billing', category: 'financial-management', isRequired: false, isVisible: true },
        { id: 'tax-management', title: 'Tax Management', path: '/financial-management/tax', category: 'financial-management', isRequired: false, isVisible: true },
      ]
    },
    {
      id: 'business-management',
      title: 'Business Management',
      items: [
        { id: 'farms', title: 'Farms', path: '/farms', category: 'business-management', isRequired: false, isVisible: true },
        { id: 'customers', title: 'Customers', path: '/customers', category: 'business-management', isRequired: false, isVisible: true },
        { id: 'products', title: 'Products', path: '/products', category: 'business-management', isRequired: false, isVisible: true },
        { id: 'grants', title: 'Agricultural Grants', path: '/grants', category: 'business-management', isRequired: false, isVisible: true },
      ]
    },
    {
      id: 'workforce-management',
      title: 'Workforce Management',
      items: [
        { id: 'hr-dashboard', title: 'HR Dashboard', path: '/hr', category: 'workforce-management', isRequired: false, isVisible: true },
        { id: 'employees', title: 'Employees', path: '/employees', category: 'workforce-management', isRequired: false, isVisible: true },
        { id: 'time-entries', title: 'Time Entries', path: '/hr/time-entries', category: 'workforce-management', isRequired: false, isVisible: true },
        { id: 'time-off-requests', title: 'Time Off Requests', path: '/hr/time-off-requests', category: 'workforce-management', isRequired: false, isVisible: true },
        { id: 'pay-stubs', title: 'Pay Stubs', path: '/hr/pay-stubs', category: 'workforce-management', isRequired: false, isVisible: true },
        { id: 'expenses', title: 'Expenses', path: '/hr/expenses', category: 'workforce-management', isRequired: false, isVisible: true },
        { id: 'seasonal-workers', title: 'Seasonal Workers', path: '/labor/seasonal-workers', category: 'workforce-management', isRequired: false, isVisible: true },
        { id: 'labor-cost-analysis', title: 'Labor Cost Analysis', path: '/labor/cost-analysis', category: 'workforce-management', isRequired: false, isVisible: true },
        { id: 'compliance-tracking', title: 'Compliance Tracking', path: '/labor/compliance', category: 'workforce-management', isRequired: false, isVisible: true },
        { id: 'worker-certifications', title: 'Worker Certifications', path: '/labor/certifications', category: 'workforce-management', isRequired: false, isVisible: true },
      ]
    },

    // Supply Chain & Resources
    {
      id: 'resource-management',
      title: 'Resource Management',
      items: [
        { id: 'inventory', title: 'Inventory', path: '/inventory', category: 'resource-management', isRequired: false, isVisible: true },
        { id: 'equipment', title: 'Equipment', path: '/equipment', category: 'resource-management', isRequired: false, isVisible: true },
        { id: 'equipment-sharing', title: 'Equipment Sharing', path: '/equipment-sharing', category: 'resource-management', isRequired: false, isVisible: true },
        { id: 'maintenance', title: 'Equipment Maintenance', path: '/maintenance', category: 'resource-management', isRequired: false, isVisible: true },
        { id: 'iot', title: 'IoT Devices', path: '/iot', category: 'resource-management', isRequired: false, isVisible: true },
      ]
    },
    {
      id: 'supply-chain',
      title: 'Supply Chain',
      items: [
        { id: 'suppliers', title: 'Supplier Marketplace', path: '/suppliers', category: 'supply-chain', isRequired: false, isVisible: true },
        { id: 'transport-dashboard', title: 'Transport Dashboard', path: '/transport', category: 'supply-chain', isRequired: false, isVisible: true },
        { id: 'drivers', title: 'Drivers', path: '/transport/drivers', category: 'supply-chain', isRequired: false, isVisible: true },
        { id: 'deliveries', title: 'Deliveries', path: '/transport/deliveries', category: 'supply-chain', isRequired: false, isVisible: true },
        { id: 'pickups', title: 'Pickups', path: '/transport/pickups', category: 'supply-chain', isRequired: false, isVisible: true },
        { id: 'driver-schedules', title: 'Driver Schedules', path: '/transport/schedules', category: 'supply-chain', isRequired: false, isVisible: true },
        { id: 'driver-locations', title: 'Driver Locations', path: '/transport/locations', category: 'supply-chain', isRequired: false, isVisible: true },
      ]
    },

    // Market & Sustainability
    {
      id: 'market-integration',
      title: 'Market & Sales',
      items: [
        { id: 'market-dashboard', title: 'Market Dashboard', path: '/market', category: 'market-integration', isRequired: false, isVisible: true },
        { id: 'market-contracts', title: 'Contracts', path: '/market/contracts', category: 'market-integration', isRequired: false, isVisible: true },
        { id: 'price-comparison', title: 'Price Comparison', path: '/market/price-comparison', category: 'market-integration', isRequired: false, isVisible: true },
        { id: 'market-trends', title: 'Market Trends', path: '/market/trends', category: 'market-integration', isRequired: false, isVisible: true },
        { id: 'marketplace', title: 'Marketplace', path: '/market/marketplace', category: 'market-integration', isRequired: false, isVisible: true },
      ]
    },
    {
      id: 'sustainability-tracking',
      title: 'Sustainability',
      items: [
        { id: 'sustainability-dashboard', title: 'Sustainability Dashboard', path: '/sustainability', category: 'sustainability-tracking', isRequired: false, isVisible: true },
        { id: 'carbon-footprint', title: 'Carbon Footprint', path: '/sustainability/carbon-footprint', category: 'sustainability-tracking', isRequired: false, isVisible: true },
        { id: 'sustainable-practices', title: 'Sustainable Practices', path: '/sustainability/practices', category: 'sustainability-tracking', isRequired: false, isVisible: true },
        { id: 'certifications', title: 'Certification Management', path: '/sustainability/certifications', category: 'sustainability-tracking', isRequired: false, isVisible: true },
        { id: 'environmental-impact', title: 'Environmental Impact', path: '/sustainability/impact', category: 'sustainability-tracking', isRequired: false, isVisible: true },
      ]
    },

    // Tools & Settings
    {
      id: 'ai-tools',
      title: 'AI Tools',
      items: [
        { id: 'ai-assistant-dashboard', title: 'AI Assistant', path: '/ai-assistant', category: 'ai-tools', isRequired: false, isVisible: true },
        { id: 'ai-decision-support', title: 'Decision Support', path: '/ai-assistant?tab=decision-support', category: 'ai-tools', isRequired: false, isVisible: true },
        { id: 'ai-predictive-maintenance', title: 'Predictive Maintenance', path: '/ai-assistant?tab=predictive-maintenance', category: 'ai-tools', isRequired: false, isVisible: true },
        { id: 'ai-harvest-recommendations', title: 'Harvest Recommendations', path: '/ai-assistant?tab=harvest-recommendations', category: 'ai-tools', isRequired: false, isVisible: true },
        { id: 'ai-field-improvement', title: 'Field Improvement', path: '/ai-assistant?tab=field-improvement', category: 'ai-tools', isRequired: false, isVisible: true },
        { id: 'ai-financial-optimization', title: 'Financial Optimization', path: '/ai-assistant?tab=financial-optimization', category: 'ai-tools', isRequired: false, isVisible: true },
        { id: 'ai-yield-profit', title: 'Yield & Profit', path: '/ai-assistant?tab=yield-profit', category: 'ai-tools', isRequired: false, isVisible: true },
      ]
    },
    {
      id: 'document-management',
      title: 'Document Management',
      items: [
        { id: 'documents', title: 'Documents', path: '/documents', category: 'document-management', isRequired: false, isVisible: true },
        { id: 'external-storage', title: 'External Storage', path: '/documents/external', category: 'document-management', isRequired: false, isVisible: true },
        { id: 'document-signing', title: 'Document Signing', path: '/documents/signing', category: 'document-management', isRequired: false, isVisible: true },
      ]
    },
    {
      id: 'integrations-settings',
      title: 'Settings',
      items: [
        { id: 'integrations', title: 'Integrations', path: '/integrations', category: 'integrations-settings', isRequired: false, isVisible: true },
        { id: 'data-migration', title: 'Data Migration', path: '/settings/data-migration', category: 'integrations-settings', isRequired: false, isVisible: true },
        { id: 'menu-customization', title: 'Menu Customization', path: '/settings/menu-customization', category: 'integrations-settings', isRequired: true, isVisible: true },
        { id: 'role-management', title: 'Role Management', path: '/settings/roles', category: 'integrations-settings', isRequired: false, isVisible: true },
        { id: 'mobile-features', title: 'Mobile Features', path: '/mobile-features', category: 'integrations-settings', isRequired: false, isVisible: true },
      ]
    },
    {
      id: 'support',
      title: 'Support',
      items: [
        { id: 'support-dashboard', title: 'Support Dashboard', path: '/support', category: 'support', isRequired: false, isVisible: true },
        { id: 'support-tickets', title: 'Support Tickets', path: '/support/tickets', category: 'support', isRequired: false, isVisible: true },
        { id: 'create-support-ticket', title: 'Create Support Ticket', path: '/support/tickets/new', category: 'support', isRequired: false, isVisible: true },
        { id: 'faq', title: 'FAQ', path: '/faq', category: 'support', isRequired: false, isVisible: true },
      ]
    },
    {
      id: 'user-settings',
      title: 'User Settings',
      items: [
        { id: 'profile', title: 'Profile', path: '/profile', category: 'user-settings', isRequired: true, isVisible: true },
        { id: 'business-account', title: 'Business Account Management', path: '/business-account', category: 'user-settings', isRequired: false, isVisible: true },
        { id: 'business-profile', title: 'Business Profile', path: '/business-profile', category: 'user-settings', isRequired: false, isVisible: true },
        { id: 'password-manager', title: 'Password Manager', path: '/password-manager', category: 'user-settings', isRequired: false, isVisible: true },
      ]
    }
  ];

  // Quick links items - improved organization aligned with sidebar categories
  const quickLinksItems: MenuItem[] = [
    // Most frequently used features
    { id: 'tasks-quick', title: 'Tasks', path: '/tasks', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'fields-quick', title: 'Fields', path: '/fields', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'transactions-quick', title: 'Transactions', path: '/transactions', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'weather-quick', title: 'Weather', path: '/weather', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'reports-quick', title: 'Reports', path: '/reports', category: 'quick-links', isRequired: false, isVisible: true },

    // Farm & Crop Management
    { id: 'crops-quick', title: 'Crops', path: '/crops', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'field-collaborations-quick', title: 'Field Collaborations', path: '/field-collaborations', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'field-health-quick', title: 'Field Health', path: '/field-health', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'crop-disease-prediction-quick', title: 'Disease Prediction', path: '/crop-management/disease-prediction', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'harvest-scheduling-quick', title: 'Harvest Scheduling', path: '/crop-management/harvest-scheduling', category: 'quick-links', isRequired: false, isVisible: true },

    // Financial & Business
    { id: 'invoices-quick', title: 'Invoices', path: '/invoices', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'receipts-quick', title: 'Receipts', path: '/receipts', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'bills-quick', title: 'Bills', path: '/bills', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'customers-quick', title: 'Customers', path: '/customers', category: 'quick-links', isRequired: false, isVisible: true },

    // Workforce & Resources
    { id: 'hr-quick', title: 'HR Dashboard', path: '/hr', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'time-off-quick', title: 'Request Time Off', path: '/hr/time-off-requests/new', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'expense-quick', title: 'Submit Expense', path: '/hr/expenses/new', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'inventory-quick', title: 'Inventory', path: '/inventory', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'equipment-quick', title: 'Equipment', path: '/equipment', category: 'quick-links', isRequired: false, isVisible: true },

    // Supply Chain & Market
    { id: 'transport-quick', title: 'Transport', path: '/transport', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'deliveries-quick', title: 'Deliveries', path: '/transport/deliveries', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'market-prices-quick', title: 'Market Prices', path: '/market-prices', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'marketplace-quick', title: 'Marketplace', path: '/market/marketplace', category: 'quick-links', isRequired: false, isVisible: true },

    // Tools & Support
    { id: 'ai-assistant-quick', title: 'AI Assistant', path: '/ai-assistant', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'documents-quick', title: 'Documents', path: '/documents', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'sustainability-quick', title: 'Sustainability', path: '/sustainability', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'support-tickets-quick', title: 'Support Tickets', path: '/support/tickets', category: 'quick-links', isRequired: false, isVisible: true },
  ];

  return {
    headerItems,
    sidebarCategories,
    quickLinksItems
  };
};

/**
 * Get mock menu preferences for development
 * @returns {MenuPreferences} - The mock menu preferences
 */
export const getMockMenuPreferences = (): MenuPreferences => {
  const defaultMenu = getDefaultMenuStructure();
  return {
    userId: 'mock-user-id',
    ...defaultMenu
  };
};
